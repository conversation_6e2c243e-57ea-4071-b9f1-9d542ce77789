#Selenium自动化，在加载时就载入cookie，无需刷新页面
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# 设置 Chrome 选项
chrome_options = Options()
chrome_options.add_argument("--start-maximized")

# 初始化 WebDriver
driver = webdriver.Chrome(options=chrome_options)

# 目标 URL 和域名
url = 'https://www.baidu.com'
domain = 'www.baidu.com'

# 解析并添加 Cookie
def parse_cookie_string(cookie_str, domain):
    cookies = []
    for item in cookie_str.strip('; ').split(';'):
        if '=' in item:
            name, value = item.strip().split('=', 1)
            value = value.strip('"\' ')  # 去除引号和空格
            cookies.append({
                'name': name,
                'value': value,
                'domain': domain
            })
    return cookies

# 从真实浏览器复制的完整cookie字符串（替换为您实际的cookie）
cookie_str = 'newlogin=1; MAWEBCUID=web_YqMBtOnwwTpmJkGeBUIyaZspszQOqVHAuEydliUaXKlSAzhMRf; BDUSS=nVmcTlLQXA2a2drMDZBckUwZHI4bUVZdkstdmlJTGJqfnl-RGhmVmQ2VUZtVnhvSVFBQUFBJCQAAAAAAAAAAAEAAABOODYAc2N4cDIwMDMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUMNWgFDDVoa; BDUSS_BFESS=nVmcTlLQXA2a2drMDZBckUwZHI4bUVZdkstdmlJTGJqfnl-RGhmVmQ2VUZtVnhvSVFBQUFBJCQAAAAAAAAAAAEAAABOODYAc2N4cDIwMDMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUMNWgFDDVoa; BD_UPN=12314753; MCITY=-173%3A75%3A; BAIDUID=8F9402153B6DA60F62D4FAF45EE83EC6:SL=0:NR=10:FG=1; BIDUPSID=8F9402153B6DA60F62D4FAF45EE83EC6; PSTM=1749540272; H_PS_PSSID=60279_61678_62325_62831_63148_63242_63327_63390_63402_63441_63456_63541_63567_63563_63582; BAIDUID_BFESS=8F9402153B6DA60F62D4FAF45EE83EC6:SL=0:NR=10:FG=1; baikeVisitId=25d61c71-db18-4d78-a426-2a8ef306de2f; delPer=0; BD_CK_SAM=1; PSINO=6; BA_HECTOR=8gakag8125048l2k802lak8l0124251k4ho2r24; ZFY=7YQtc2irg4y3tg:AJkt:Ab6sYlq9CT0DfbPEGh53U3NlA:C; BDRCVFR[bPTzwF-RsLY]=mk3SLVN4HKm; BDORZ=FFFB88E999055A3F8A630C64834BD6D0; H_WISE_SIDS=60279_61678_62325_62831_63148_63242_63327_63390_63402_63441_63456_63541_63567_63563_63582; ZD_ENTRY=baidu; sug=3; sugstore=1; ORIGIN=0; bdime=0; ab_sr=1.0.1_OTVkZjA0OWEzYTQxNWIzNTk3Y2UyMTgwMzAxZTA2ZTZkYjkzYWRlMmU2NmFkNGZmZTc5ZGU4MzRlZmYyMTdmODcxN2I2MTI5ZThjNWIxMjUzZjllOTRhNmM3MGFiZWY2YzAwMzczN2ExOTBjMTNmNTgxNzExODYzZmZlZDJmYjI1NmQ1OTU3OTljNGZiZDk1YmUyNTRlYjc4MWNmNWRhYw==; RT="z=1&dm=baidu.com&si=7aff96d7-2f24-4bcb-a6c3-9ce9972c239e&ss=mbrcmm0x&sl=1&tt=31d&bcn=https%3A%2F%2Ffclog.baidu.com%2Flog%2Fweirwood%3Ftype%3Dperf&ld=1evxq"'

# 先访问域名以建立上下文
driver.get(f'https://{domain}')

# 解析并添加所有从浏览器复制的cookie
for cookie in parse_cookie_string(cookie_str, domain):
    driver.add_cookie(cookie)

# 访问目标页面，cookie已生效
driver.get(url)

# 等待页面加载完成
WebDriverWait(driver, 10).until(
    EC.presence_of_element_located((By.ID, "kw"))
)

# 后续操作
search_box = driver.find_element(By.ID, "kw")
search_box.send_keys("自贡灯会")

# 保持浏览器窗口打开
try:
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    print("手动关闭浏览器窗口")

# 关闭浏览器
driver.quit()