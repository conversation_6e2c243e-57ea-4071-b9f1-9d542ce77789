<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>书签点击统计</title>
  <link rel="stylesheet" href="popup.css">
  <!-- 添加 Font Awesome 图标库 -->
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <div class="container">
    <h1>
        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0799 24L4 19.2479L9.95537 8.75216L18.04 13.4961L18.0446 4H29.9554L29.96 13.4961L38.0446 8.75216L44 19.2479L35.92 24L44 28.7521L38.0446 39.2479L29.96 34.5039L29.9554 44H18.0446L18.04 34.5039L9.95537 39.2479L4 28.7521L12.0799 24Z" fill="currentColor"></path>
                </svg>书签点击统计
        <span id="bookmarkCount" style="font-size: 0.8em; color: #555; margin-left: 5px;" title="当前书签总数统计"></span>
      </h1>
    <div class="controls">
      <div class="search-wrapper">
        <input type="text" id="searchInput" placeholder="搜索书签...">
        <button id="clearSearchInput" title="清空搜索">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <select id="sortOrder">
        <option value="title_asc">名称 (A-Z)</option>
        <option value="title_desc">名称 (Z-A)</option>
        <option value="count_desc" selected>点击次数 (多到少)</option>
        <option value="count_asc">点击次数 (少到多)</option>
        <option value="lastClicked_desc">最近点击 (新到旧)</option>
        <option value="lastClicked_asc">最近点击 (旧到新)</option>
      </select>
      <button id="resetAllButton" title="重置所有统计">
        <i class="fas fa-redo-alt"></i>
      </button>
    </div>    
    <div id="bookmarksList">
      <p>正在加载书签数据...</p>
    </div>
  </div>
  <div class="footer">
    <a href="popup.html" target="_blank" title="在新窗口中打开，获得更大的操作空间">
      <i class="fas fa-external-link-alt"></i> 新窗口打开
    </a>
    <div class="option-group">
      <input type="checkbox" id="editMode" name="editMode">
      <label for="editMode">编辑模式</label>
    </div>
  </div>
  <script src="popup.js"></script>
</body>
</html>