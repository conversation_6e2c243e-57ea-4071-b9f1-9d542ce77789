<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mindful - Your Safe Space for Reflection</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.fog.min.js"></script>
    <style>
        .wave-bg {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23A78BFA' fill-opacity='0.1' d='M0,128L48,144C96,160,192,192,288,186.7C384,181,480,139,576,138.7C672,139,768,181,864,181.3C960,181,1056,139,1152,133.3C1248,128,1344,160,1392,176L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
            background-size: cover;
            background-position: center;
        }
        .wave-bg-top {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23A78BFA' fill-opacity='0.05' d='M0,224L60,229.3C120,235,240,245,360,229.3C480,213,600,171,720,170.7C840,171,960,213,1080,218.7C1200,224,1320,192,1380,176L1440,160L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z'%3E%3C/path%3E%3C/svg%3E");
            background-size: cover;
            background-position: center;
        }
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.8s ease-out forwards;
        }
        .delay-1 { animation-delay: 0.3s; }
        .delay-2 { animation-delay: 0.6s; }
        .delay-3 { animation-delay: 0.9s; }
        .delay-4 { animation-delay: 1.2s; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-[#F8F8F7] font-[Quicksand,sans-serif]">
    <div id="bg-animation" class="relative overflow-hidden min-h-screen">
        <!-- Wave backgrounds -->
        <div class="absolute inset-x-0 bottom-0 h-64 wave-bg -z-10 opacity-70"></div>
        <div class="absolute inset-x-0 top-0 h-64 wave-bg-top -z-10 opacity-70"></div>
        
        <!-- Navbar -->
        <nav class="container mx-auto px-6 py-6 relative z-10">
            <div class="flex items-center justify-between fade-in">
                <div class="text-2xl font-semibold text-gray-800">mindful<span class="text-[#A78BFA]">.</span></div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-gray-600 focus:outline-none">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- Desktop menu -->
                <div class="hidden md:flex space-x-8">
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors">Features</a>
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors">Pricing</a>
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors">Resources</a>
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors">About</a>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors">Login</a>
                    <a href="#" class="px-4 py-2 text-sm text-[#A78BFA] border border-[#A78BFA] rounded-full hover:bg-[#A78BFA] hover:text-white transition-colors">Sign Up</a>
                </div>
            </div>
            
            <!-- Mobile menu (hidden by default) -->
            <div id="mobile-menu" class="md:hidden hidden pt-4 pb-2 fade-in">
                <div class="flex flex-col space-y-3 px-2 bg-white/80 backdrop-blur-sm rounded-lg p-4">
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors py-2">Features</a>
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors py-2">Pricing</a>
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors py-2">Resources</a>
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors py-2">About</a>
                    <a href="#" class="text-gray-600 hover:text-[#A78BFA] transition-colors py-2">Login</a>
                    <a href="#" class="text-center px-4 py-2 text-sm text-[#A78BFA] border border-[#A78BFA] rounded-full hover:bg-[#A78BFA] hover:text-white transition-colors">Sign Up</a>
                </div>
            </div>
        </nav>
        
        <!-- Documentation Section -->
        <section class="container mx-auto px-6 py-12 relative z-10">
            <div class="max-w-4xl mx-auto">
                <!-- Header -->
                <div class="text-center mb-16 fade-in delay-1">
                    <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
                        Your Safe Space for <span class="text-[#A78BFA]">Reflection</span>
                    </h1>
                    <div class="w-24 h-1 bg-gradient-to-r from-[#A78BFA] to-purple-400 mx-auto rounded-full"></div>
                </div>
                
                <!-- Feature Grid -->
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- Digital Journal Entries -->
                    <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 fade-in delay-2">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-[#A78BFA]/20 rounded-xl flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-[#A78BFA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">Digital Journal Entries</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Capture your thoughts and feelings with guided daily prompts designed to encourage self-reflection and emotional awareness.</p>
                        <div class="text-sm text-[#A78BFA] font-medium">Daily prompts available</div>
                    </div>
                    
                    <!-- Thought Logs -->
                    <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 fade-in delay-3">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-[#A78BFA]/20 rounded-xl flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-[#A78BFA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">Thought Logs</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Practice CBT-style thought reframing exercises to identify patterns and develop healthier thinking habits.</p>
                        <div class="text-sm text-[#A78BFA] font-medium">CBT-based approach</div>
                    </div>
                    
                    <!-- Meditation History -->
                    <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 fade-in delay-4">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-[#A78BFA]/20 rounded-xl flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-[#A78BFA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">Meditation History</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Track your mindfulness journey with detailed logs of past meditation sessions and progress insights.</p>
                        <div class="text-sm text-[#A78BFA] font-medium">Progress tracking included</div>
                    </div>
                    
                    <!-- PDF Export -->
                    <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 fade-in delay-4">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-[#A78BFA]/20 rounded-xl flex items-center justify-center mr-4">
                                <svg class="w-6 h-6 text-[#A78BFA]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800">PDF Export Option</h3>
                        </div>
                        <p class="text-gray-600 mb-4">Share your progress with healthcare providers through secure PDF exports for comprehensive therapist review.</p>
                        <div class="text-sm text-[#A78BFA] font-medium">Therapist collaboration ready</div>
                    </div>
                </div>
                
                <!-- Call to Action -->
                <div class="text-center mt-16 fade-in delay-4">
                    <a href="#" class="inline-block px-8 py-4 text-white font-medium bg-[#A78BFA] rounded-full shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300">
                        Start Your Reflection Journey
                    </a>
                </div>
            </div>
        </section>
    </div>
    
    <script>
        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
        
        // Initialize VANTA.FOG
        VANTA.FOG({
            el: "#bg-animation",
            mouseControls: true,
            touchControls: true,
            gyroControls: false,
            minHeight: 200.00,
            minWidth: 200.00,
            highlightColor: 0xd4a7f0,
            midtoneColor: 0x63c5b7,
            lowlightColor: 0x568bfa,
            blurFactor: 0.73,
            speed: 2.10,
            zoom: 0.50
        });
    </script>
</body>
</html>