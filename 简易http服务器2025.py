import http.server
import socketserver
import webbrowser

# 定义服务器的端口号
PORT = 8000

# 允许端口复用
socketserver.TCPServer.allow_reuse_address = True

# 使用 SimpleHTTPRequestHandler 提供文件服务
Handler = http.server.SimpleHTTPRequestHandler

# 使用 ThreadingTCPServer 替代 TCPServer 来支持多线程
with socketserver.ThreadingTCPServer(("", PORT), Handler) as httpd:
    print(f"文件服务器已启动，正在监听端口 {PORT}...")
    url = f"http://localhost:{PORT}"
    print(f"请在浏览器中访问 {url} 查看文件。")
    webbrowser.open(url)
    try:
        httpd.serve_forever()  # 持续运行服务器
    except KeyboardInterrupt:
        print("\n服务器已停止。")
