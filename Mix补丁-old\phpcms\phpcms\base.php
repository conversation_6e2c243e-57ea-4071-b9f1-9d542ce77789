<?php
/**
 *  base.php BDCMS框架入口文件
 *
 * @copyright			(C) 2013 BDCMS
 * @license				http://www.cdbaidu.com
 * @lastmodify			2013-6-7
 */
error_reporting(E_ALL^E_NOTICE);
define('IN_PHPCMS', true);
define('DS', DIRECTORY_SEPARATOR);
//PHPCMS框架路径
define('PC_PATH', dirname(__FILE__).DS);

if(!defined('PHPCMS_PATH')) define('PHPCMS_PATH', PC_PATH.'..'.DS);

//缓存文件夹地址
define('CACHE_PATH', PHPCMS_PATH.'caches'.DS);
//主机协议
define('SITE_PROTOCOL', isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '443' ? 'https://' : 'http://');
//当前访问的主机名
define('SITE_URL', (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : ''));
//来源
define('HTTP_REFERER', isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '');
//系统开始时间
define('SYS_START_TIME', microtime());

//加载公用函数库
pc_base::load_sys_func('global');
pc_base::load_sys_func('extention');
pc_base::auto_load_func();

pc_base::load_config('system','errorlog') ? set_error_handler('my_error_handler') : error_reporting(E_ERROR | E_WARNING | E_PARSE);
//设置本地时差
function_exists('date_default_timezone_set') && date_default_timezone_set(pc_base::load_config('system','timezone'));

define('CHARSET' ,pc_base::load_config('system','charset'));
//输出页面字符集
header('Content-type: text/html; charset='.CHARSET);

define('SYS_TIME', time());
//定义网站根路径
define('WEB_PATH',pc_base::load_config('system','web_path'));
//js 路径
define('JS_PATH',pc_base::load_config('system','js_path'));
//css 路径
define('CSS_PATH',pc_base::load_config('system','css_path'));
//img 路径
define('IMG_PATH',pc_base::load_config('system','img_path'));
//动态程序路径
define('APP_PATH',pc_base::load_config('system','app_path'));

//应用静态文件路径
define('PLUGIN_STATICS_PATH',WEB_PATH.'statics/plugin/');


if(pc_base::load_config('system','gzip') && function_exists('ob_gzhandler')) {
	ob_start('ob_gzhandler');
} else {
	ob_start();
}

// pw 20230321 设置PHPSESSID的HttpOnly属性为true
session_start();
$sess_params = session_get_cookie_params();
$isHttps = is_https();
setcookie("PHPSESSID", session_id(), 0, $sess_params["path"], $sess_params["domain"],
    $isHttps,
    true
);

//pw 2013/7/5 修改每套模板使用独立前台文件，站点ID指定为1
$sitelist  = getcache('sitelist','commons');
$site_style = mobileHeader( $sitelist[1]['template'] );


/* 载入站点参数 */
$_CFG = $sitelist[1];

/* 强制跳转https */
$param = pc_base::load_sys_class('param');
if($param->route_m() !== 'admin' && (int)$_CFG['https_force'] && !$isHttps){
	header("Location: https://" . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']);
	exit();
}



//当前站点模板
define('SITE_TPL',$_CFG['template']);
define('SKIN_PATH',str_replace('\\','/',WEB_PATH.'phpcms'.DS.'templates'.DS.$site_style.DS.'skin/'));

/* pw20150506 */
$sitedir=str_replace(str_replace('\\','/',$_SERVER['DOCUMENT_ROOT']),'',str_replace('\\','/',PHPCMS_PATH));
//定义公共模块模板
//PC_PATH.DS.'templates'.DS.SITE_TPL.DS.'m_tpl'
define('M_TPL',SKIN_PATH.'_module/');

class pc_base {
	
	public static function safeFilter (&$arr) {
     
	   $ra=Array('/[\x00-\x08\x0b-\x0c\x0e-\x19]/','/script/i','/javascript/i','/vbscript/i','/expression/i','/applet/i','/meta/i','/xml/i','/blink/i','/link/i','/style/i','/embed/i','/object/i','/frame/i','/layer/i','/title/i','/bgsound/i','/base/i','/onload/i','/onunload/i','/onchange/i','/onsubmit/i','/onreset/i','/onselect/i','/onblur/i','/onfocus/i','/onabort/i','/onkeydown/i','/onkeypress/i','/onkeyup/i','/onclick/i','/ondblclick/i','/onmousedown/i','/onmousemove/i','/onmouseout/i','/onmouseover/i','/onmouseup/i','/onunload/i');
	     
	   if (is_array($arr)){
	     foreach ($arr as $key => $value) {
	        if (!is_array($value)){
	          if (!get_magic_quotes_gpc()){
	             // $value  = addslashes($value);
	          }
	          $value       = preg_replace($ra,'',$value);
	          $arr[$key]     = htmlspecialchars(($value), ENT_QUOTES);
	        }
	        else{
	          safeFilter($arr[$key]);
	        }
	     }
	   }
	}	
	
	//pw 2014/1/16
	public static $_CFG;
	
	public static function siteCof(){
		global $_CFG;
		self::$_CFG=&$_CFG;
	}
	/**
	 * 初始化应用程序
	 */
	public static function creat_app() {
		return self::load_sys_class('application');
	}
	/**
	 * 加载系统类方法
	 * @param string $classname 类名
	 * @param string $path 扩展地址
	 * @param intger $initialize 是否初始化
	 */
	public static function load_sys_class($classname, $path = '', $initialize = 1) {
			return self::_load_class($classname, $path, $initialize);
	}
	
	/**
	 * 加载应用类方法
	 * @param string $classname 类名
	 * @param string $m 模块
	 * @param intger $initialize 是否初始化
	 */
	public static function load_app_class($classname, $m = '', $initialize = 1) {
		$m = empty($m) && defined('ROUTE_M') ? ROUTE_M : $m;
		if (empty($m)) return false;
		return self::_load_class($classname, 'modules'.DS.$m.DS.'classes', $initialize);
	}
	
	/**
	 * 加载数据模型
	 * @param string $classname 类名
	 */
	public static function load_model($classname) {
		return self::_load_class($classname,'model');
	}
		
	/**
	 * 加载类文件函数
	 * @param string $classname 类名
	 * @param string $path 扩展地址
	 * @param intger $initialize 是否初始化
	 */
	private static function _load_class($classname, $path = '', $initialize = 1) {
		static $classes = array();
		if (empty($path)) $path = 'libs'.DS.'classes';

		$key = md5($path.$classname);
		if (isset($classes[$key])) {
			if (!empty($classes[$key])) {
				return $classes[$key];
			} else {
				return true;
			}
		}
		if (file_exists(PC_PATH.$path.DS.$classname.'.class.php')) {
			include PC_PATH.$path.DS.$classname.'.class.php';
			$name = $classname;
			if ($my_path = self::my_path(PC_PATH.$path.DS.$classname.'.class.php')) {
				include $my_path;
				$name = 'MY_'.$classname;
			}
			if ($initialize) {
				$classes[$key] = new $name;
			} else {
				$classes[$key] = true;
			}
			return $classes[$key];
		} else {
			return false;
		}
	}
	
	/**
	 * 加载系统的函数库
	 * @param string $func 函数库名
	 */
	public static function load_sys_func($func) {
		return self::_load_func($func);
	}
	
	/**
	 * 自动加载autoload目录下函数库
	 * @param string $func 函数库名
	 */
	public static function auto_load_func($path='') {
		return self::_auto_load_func($path);
	}
	
	/**
	 * 加载应用函数库
	 * @param string $func 函数库名
	 * @param string $m 模型名
	 */
	public static function load_app_func($func, $m = '') {
		$m = empty($m) && defined('ROUTE_M') ? ROUTE_M : $m;
		if (empty($m)) return false;
		return self::_load_func($func, 'modules'.DS.$m.DS.'functions');
	}
	
	/**
	 * 加载插件类库
	 */
	public static function load_plugin_class($classname, $identification = '' ,$initialize = 1) {
		$identification = empty($identification) && defined('PLUGIN_ID') ? PLUGIN_ID : $identification;
		if (empty($identification)) return false;
		return pc_base::load_sys_class($classname, 'plugin'.DS.$identification.DS.'classes', $initialize);
	}
	
	/**
	 * 加载插件函数库
	 * @param string $func 函数文件名称
	 * @param string $identification 插件标识
	 */
	public static function load_plugin_func($func,$identification) {
		static $funcs = array();
		$identification = empty($identification) && defined('PLUGIN_ID') ? PLUGIN_ID : $identification;
		if (empty($identification)) return false;
		$path = 'plugin'.DS.$identification.DS.'functions'.DS.$func.'.func.php';
		$key = md5($path);
		if (isset($funcs[$key])) return true;
		if (file_exists(PC_PATH.$path)) {
			include PC_PATH.$path;
		} else {
			$funcs[$key] = false;
			return false;
		}
		$funcs[$key] = true;
		return true;
	}
	
	/**
	 * 加载插件数据模型
	 * @param string $classname 类名
	 */
	public static function load_plugin_model($classname,$identification) {
		$identification = empty($identification) && defined('PLUGIN_ID') ? PLUGIN_ID : $identification;
		$path = 'plugin'.DS.$identification.DS.'model';
		return self::_load_class($classname,$path);
	}
	
	/**
	 * 加载函数库
	 * @param string $func 函数库名
	 * @param string $path 地址
	 */
	private static function _load_func($func, $path = '') {
		static $funcs = array();
		if (empty($path)) $path = 'libs'.DS.'functions';
		$path .= DS.$func.'.func.php';
		$key = md5($path);
		if (isset($funcs[$key])) return true;
		if (file_exists(PC_PATH.$path)) {
			include PC_PATH.$path;
		} else {
			$funcs[$key] = false;
			return false;
		}
		$funcs[$key] = true;
		return true;
	}
	
	/**
	 * 加载函数库
	 * @param string $func 函数库名
	 * @param string $path 地址
	 */
	private static function _auto_load_func($path = '') {
		if (empty($path)) $path = 'libs'.DS.'functions'.DS.'autoload';
		$path .= DS.'*.func.php';
		$auto_funcs = glob(PC_PATH.DS.$path);
		if(!empty($auto_funcs) && is_array($auto_funcs)) {
			foreach($auto_funcs as $func_path) {
				include $func_path;
			}
		}
	}
	/**
	 * 是否有自己的扩展文件
	 * @param string $filepath 路径
	 */
	public static function my_path($filepath) {
		$path = pathinfo($filepath);
		if (file_exists($path['dirname'].DS.'MY_'.$path['basename'])) {
			return $path['dirname'].DS.'MY_'.$path['basename'];
		} else {
			return false;
		}
	}
	
	/**
	 * 加载配置文件
	 * @param string $file 配置文件
	 * @param string $key  要获取的配置荐
	 * @param string $default  默认配置。当获取配置项目失败时该值发生作用。
	 * @param boolean $reload 强制重新加载。
	 */
	public static function load_config($file, $key = '', $default = '', $reload = false) {
		//pw 2014/1/16
		if(empty(self::$_CFG))self::siteCof();
		
		static $configs = array();
		if (!$reload && isset($configs[$file])) {
			if (empty($key)) {
				return $configs[$file];
			} elseif (isset($configs[$file][$key])) {
				return $configs[$file][$key];
			} else {
				return $default;
			}
		}
		$path = CACHE_PATH.'configs'.DS.$file.'.php';
		if (file_exists($path)) {
			$configs[$file] = include $path;
		}
		if (empty($key)) {
			return $configs[$file];
		} elseif (isset($configs[$file][$key])) {
			return $configs[$file][$key];
		} else {
			return $default;
		}
	}
}