import os
import zipfile
from pathlib import Path
import tempfile
import shutil

def update_zip_with_phpcms():
    # 获取当前目录
    current_dir = Path.cwd()
    phpcms_dir = current_dir / 'phpcms'
    
    # 确保phpcms文件夹存在
    if not phpcms_dir.exists():
        print("错误: 未找到phpcms文件夹")
        return
    
    # 遍历所有zip文件
    for zip_file in current_dir.glob('*.zip'):
        print(f"处理: {zip_file.name}")
        
        try:
            # 创建临时文件
            temp_zip = tempfile.NamedTemporaryFile(delete=False)
            temp_zip.close()
            
            # 复制原始zip到临时文件
            shutil.copy2(zip_file, temp_zip.name)
            
            with zipfile.ZipFile(zip_file, 'r') as zf_old:
                # 检查zip中是否存在phpcms文件夹（最多检查2级子目录）
                phpcms_prefix = None
                for name in zf_old.namelist():
                    parts = Path(name).parts
                    if len(parts) <= 3:  # 最多检查2级子目录
                        if 'phpcms' in parts:
                            phpcms_prefix = '/'.join(parts[:parts.index('phpcms')])
                            if phpcms_prefix:
                                phpcms_prefix += '/'
                            break
                
                if phpcms_prefix is None:
                    print(f"跳过 {zip_file.name}: 未找到phpcms文件夹")
                    os.unlink(temp_zip.name)
                    continue
                
                # 创建新的zip文件
                with zipfile.ZipFile(temp_zip.name, 'w') as zf_new:
                    # 复制不需要更新的文件和phpcms中不需要更新的文件
                    for item in zf_old.infolist():
                        # 获取本地phpcms中对应的文件路径
                        if phpcms_prefix + 'phpcms/' in item.filename:
                            # 计算相对于phpcms的路径
                            relative_to_phpcms = item.filename.split(phpcms_prefix + 'phpcms/')[1]
                            local_path = Path(current_dir) / 'phpcms' / relative_to_phpcms
                            # 如果本地没有这个文件，保留原始文件
                            if not local_path.exists():
                                zf_new.writestr(item, zf_old.read(item.filename))
                        else:
                            zf_new.writestr(item, zf_old.read(item.filename))
                    
                    # 添加更新的文件
                    for root, _, files in os.walk(phpcms_dir):
                        for file in files:
                            file_path = Path(root) / file
                            # 计算相对路径，添加到正确的子目录中
                            relative_to_phpcms = file_path.relative_to(phpcms_dir)
                            zip_path = f"{phpcms_prefix}phpcms/{relative_to_phpcms}"
                            print(f"更新: {zip_path}")
                            zf_new.write(file_path, zip_path)
            
            # 替换原始文件
            os.unlink(zip_file)
            shutil.move(temp_zip.name, zip_file)
            print(f"完成更新: {zip_file.name}")
                
        except Exception as e:
            print(f"处理 {zip_file.name} 时出错: {str(e)}")
            # 清理临时文件
            if os.path.exists(temp_zip.name):
                os.unlink(temp_zip.name)

if __name__ == "__main__":
    update_zip_with_phpcms()