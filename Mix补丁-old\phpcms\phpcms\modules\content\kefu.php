<?php
defined('IN_PHPCMS') or exit('No permission resources.');
//模型缓存路径
define('CACHE_MODEL_PATH',CACHE_PATH.'caches_model'.DIRECTORY_SEPARATOR.'caches_data'.DIRECTORY_SEPARATOR);
pc_base::load_app_func('util','content');
class kefu {
	private $db;
	private static $salt = '0dV1a7Vi';

	function __construct() {

	}
	//pw 2014/1/20 QQ客服
	function qqkefu(){
		$kefu_conf=unserialize(pc_base::$_CFG['kefu_conf']);
		if(isset($kefu_conf['kefu_switch']) && !$kefu_conf['kefu_switch'])exit();
		if(!isset($_GET['q'])){
			if(!empty($kefu_conf)){
				$timestamp = time();
				$kefu_list = explode("\r\n",$kefu_conf['qq_no']);
				if(!empty($kefu_conf['td_code']) && preg_match('/^(https?):\/\/([a-zA-Z0-9.-]+)(:[0-9]+)?(\/[^\s]*)?$/i',$kefu_conf['td_code'])){
					$qrurl = urlencode($kefu_conf['td_code']);
					$kefu_conf['td_image']='/api.php?op=byqr&size=5&url='. $qrurl . '&ts=' . $timestamp . '&sk=' . md5($timestamp . kefu::$salt . $qrurl);	
				}
			}
			include template('content',$kefu_conf['kefu_style']=='kefu_customer'?'kefu_customer':'qqkefu');
		}else{
			$file = fopen('http://'.$_SERVER['HTTP_HOST'].'/index.php?c=kefu&a=qqkefu&rand='.mt_rand(),'r');

			while(! feof($file))
			{
				$html.='"'.addslashes(trim(fgets($file))).'"+';
			}
			
			fclose($file);
			
			echo '$(function(){'
				.'var ie8=false;'
				.'if(/msie 8\.0/i.test(window.navigator.userAgent.toLowerCase())){'
				.'ie8=true;var dynamicLoading = {'
				.'css:function(path){'
				.'if(!path || path.length === 0){'
				.'throw new Error(\'argument "path" is required !\');'
				.'}'
				.'var head = document.getElementsByTagName("head")[0];'
				.'var link = document.createElement("link");'
				.'link.href = path;'
				.'link.rel = "stylesheet";'
				.'link.type = "text/css";'
				.'head.appendChild(link);'
				.'},'
				.'js:function(path){'
				.'if(!path || path.length === 0){'
				.'throw new Error(\'argument "path" is required !\');'
				.'}'
				.'var head = document.getElementsByTagName("head")[0];'
				.'var script = document.createElement("script");'
				.'script.setAttribute("type", "text/javascript");'
                .'script.setAttribute("src", path);'
				.'head.appendChild(script);'
				.'}'
				.'};'
				.'dynamicLoading.js("'.SKIN_PATH.'js/kefu_online.js");'
				.'dynamicLoading.css("'.SKIN_PATH.'css/kefu/kefu_common.css");'
				.'dynamicLoading.css("'.SKIN_PATH.'css/kefu/'.$kefu_conf['kefu_style'].'/style.css");'
				.'}else{'
				.'$("head").eq(0).append("<link type=\"text/css\" rel=\"stylesheet\" href=\"'.SKIN_PATH.'css/kefu/kefu_common.css\" \/>").append("<link type=\"text/css\" rel=\"stylesheet\" href=\"'.SKIN_PATH.'css/kefu/'.$kefu_conf['kefu_style'].'/style.css\" \/>").append("<script language=\"javascript\" src=\"'.SKIN_PATH.'js/kefu_online.js\"></script>");'
				.'}'
				.'var kefu_html='.rtrim($html,'+').';'
				.'$("body").append(kefu_html);'
				.'try{$("#kefu_wrap").showService({direction:"'.$kefu_conf['kefu_pos'].'",position:"fixed",defShow:true,Event:"'.$kefu_conf['kefu_show_way'].'"});}catch(e){setTimeout(function(){'
				.'$("#kefu_wrap").showService({direction:"'.$kefu_conf['kefu_pos'].'",position:"fixed",defShow:true,Event:"'.$kefu_conf['kefu_show_way'].'"});},500);};'
				.'});';
		}
	}
}
?>