<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>随机密码生成器</title>
    <style>
        /* 整体页面样式 */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(to right bottom, #e0ecff, #f5f7fa);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            color: #333;
        }
    
        /* 容器样式增强 */
        .container {
            background-color: #ffffffcc;
            padding: 2.5em;
            border-radius: 15px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: 450px;
            text-align: center;
            backdrop-filter: blur(10px); /* 玻璃效果背景 */
        }
    
        h1 {
            color: #007BFF;
            font-weight: bold;
            margin-bottom: 1em;
        }
    
        label {
            display: block;
            margin-top: 1em;
            text-align: left;
            font-weight: 600;
        }
    
        input[type=number], select, input[type=checkbox] {
            width: 100%;
            padding: 0.6em;
            margin-top: 0.2em;
            border: 1px solid #aaa;
            border-radius: 6px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }
    
        input[type=checkbox] {
            width: auto;
            margin-right: 0.5em;
        }
    
        .checkbox-group {
            text-align: left;
            display: flex;
            flex-direction: column;
            gap: 0.3em;
        }
    
        button {
            margin-top: 1.5em;
            width: 100%;
            padding: 0.8em;
            background: linear-gradient(to right, #007BFF, #0056b3);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: background 0.3s ease, transform 0.2s ease;
        }
    
        button:hover {
            background: linear-gradient(to right, #005fcc, #004a99);
            transform: scale(1.02);
        }
    
        .output {
            margin-top: 1.5em;
            font-size: 1.3em;
            word-wrap: break-word;
            color: #222;
            min-height: 2em;
            padding: 20px;
            border: 2px dashed #007BFF;
            min-height: 40px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            font-size: 24px;
            color: #333;
            max-width: 500px;
        }
    
        #copyMessage {
            margin-top: 0.5em;
            font-size: 1em;
            min-height: 1.5em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒随机密码生成器</h1>
        <form id="passwordForm">
            <label for="length">密码长度:</label>
            <input type="number" id="length" name="length" min="1" value="8" required>

            <label>字符集 (可多选):</label>
            <div class="checkbox-group">
                <label><input type="checkbox" name="characterSet" value="letters" checked> 字母 (a-z, A-Z)</label> <!-- 合并后的选项 -->
                <label><input type="checkbox" name="characterSet" value="numbers" checked> 数字 (0-9)</label>
                <label><input type="checkbox" name="characterSet" value="symbols" checked> 符号 (!@#$%^&*(),.?)</label>
            </div>

            <div class="checkbox-group"> 
                <label><input type="checkbox" id="excludeSimilar" checked> 排除相似字符 (i, l, 1, L, o, 0, O)</label>
            </div>

            <button type="submit">📋生成密码</button>
        </form>
        <div class="output" id="passwordOutput">密码将显示在这里</div>
        <div id="copyMessage" style="margin-top: 0.5em; font-size: 1em; color: green;"></div>
    </div>

    <script>
        document.getElementById('passwordForm').addEventListener('submit', function(event) {
            event.preventDefault();

            const length = parseInt(document.getElementById('length').value);
            const selectedCharsets = document.querySelectorAll('input[name="characterSet"]:checked');
            const excludeSimilar = document.getElementById('excludeSimilar').checked;

            let characters = '';
            selectedCharsets.forEach(checkbox => {
                // 修改这里：检查 'letters' 值并添加大小写字母
                if (checkbox.value === 'letters') characters += 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
                if (checkbox.value === 'numbers') characters += '0123456789';
                if (checkbox.value === 'symbols') characters += '!@#$%^&*(),.?';
            });

            // 如果没有选择任何字符集，则提示用户
            if (characters === '') {
                document.getElementById('passwordOutput').textContent = '请至少选择一个字符集！';
                document.getElementById('copyMessage').textContent = '';
                return;
            }

            if (excludeSimilar) {
                characters = characters.split('').filter(c => !'il1Lo0O'.includes(c)).join('');
                 if (characters === '') {
                    document.getElementById('passwordOutput').textContent = '排除相似字符后，可用字符集为空！';
                    document.getElementById('copyMessage').textContent = '';
                    return;
                }
            }

            let password = '';
            const charactersLength = characters.length;

            for (let i = 0; i < length; i++) {
                const randomIndex = Math.floor(Math.random() * charactersLength);
                password += characters[randomIndex];
            }

            document.getElementById('passwordOutput').textContent = '生成的密码: ' + password;
            // 复制密码到剪贴板
            navigator.clipboard.writeText(password).then(function() {
                document.getElementById('copyMessage').textContent = '已成功复制到剪贴板！';
                setTimeout(function() {
                    document.getElementById('copyMessage').textContent = '';
                }, 2000); // 提示消失时间
            }, function(err) {
                console.error('无法复制密码: ', err);
                document.getElementById('copyMessage').textContent = '复制失败，请手动复制。'; // 提供错误反馈
                 setTimeout(function() {
                    document.getElementById('copyMessage').textContent = '';
                }, 3000);
            });
        });
    </script>
</body>
</html>