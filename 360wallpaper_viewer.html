<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>360壁纸API展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            padding: 20px;
        }
        header {
            text-align: center;
            margin-bottom: 30px;
        }
        h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #333;
        }
        .loading {
            text-align: center;
            margin: 20px 0;
            font-size: 16px;
        }
        .error {
            color: #f44336;
            text-align: center;
            margin: 20px 0;
            font-size: 16px;
            display: none;
        }
        .wallpaper-container {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        .wallpaper-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .wallpaper-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .wallpaper-image {
            width: 100%;
            /* 根据16:9的比例计算高度 */
            height: calc(100vw / 5 / 16 * 9);
            object-fit: cover;
            display: block;
        }
        .wallpaper-info {
            padding: 15px;
        }
        .wallpaper-resolution {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .wallpaper-tags {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            min-height: 25px;
        }
        .wallpaper-tag {
            display: inline-block;
            background-color: #e9e9e9;
            padding: 3px 8px;
            border-radius: 4px;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .download-links {
            margin-top: 10px;
        }
        .download-link {
            display: inline-block;
            background-color: #2196F3;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            text-decoration: none;
            margin-right: 5px;
            margin-bottom: 5px;
            font-size: 12px;
            transition: background-color 0.3s;
        }
        .download-link:hover {
            background-color: #0b7dda;
        }
        .no-link{
            padding: 0;
            background-color:transparent;
            color: #000;
        }
        .no-link:hover{
            background-color:transparent;
            color: #000;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 30px;
            gap: 10px;
        }
        .pagination button {
            background-color: #ddd;
            color: #333;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .pagination button.active {
            background-color: #4CAF50;
            color: white;
        }
        .pagination button:hover {
            background-color: #ccc;
        }
        .api-info {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
        /* 图片预览相关样式 */
        .lightbox-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }
        
        .lightbox-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .lightbox-image {
            max-width: 90%;
            max-height: 90%;
            object-fit: contain;
            border: 2px solid white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            transform: scale(0.9);
            transition: transform 0.3s;
        }
        
        .lightbox-overlay.active .lightbox-image {
            transform: scale(1);
        }
        
        .close-button {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 30px;
            cursor: pointer;
            background: none;
            border: none;
            opacity: 0.7;
            transition: opacity 0.3s;
        }
        
        .close-button:hover {
            opacity: 1;
        }
        
        /* 为缩略图添加鼠标样式，表明可点击 */
        .wallpaper-image {
            cursor: pointer;
        }
    </style>
</head>
<body>
    <header>
        <h1>360壁纸API展示</h1>
        <p>展示来自360壁纸API的精美壁纸</p>
    </header>

    <div id="loading" class="loading">正在加载壁纸数据，请稍候...</div>
    <div id="error" class="error">加载失败，请检查网络连接或稍后再试</div>

    <div id="wallpaperContainer" class="wallpaper-container"></div>

    <div id="pagination" class="pagination"></div>

    <div class="api-info">
        <p>数据来源: <a href="https://api.aa1.cn/doc/api-meiribizhi.html" target="_blank">夏柔API</a></p>
    </div>
    <!-- 图片预览容器 -->
    <div id="lightboxOverlay" class="lightbox-overlay">
        <img id="lightboxImage" class="lightbox-image" src="" alt="壁纸预览">
        <button id="closeButton" class="close-button">×</button>
    </div>
    <script>
        // API地址
        const API_URL = 'https://v.api.aa1.cn/api/api-meiribizhi/api.php';
        
        // DOM元素
        const loadingElement = document.getElementById('loading');
        const errorElement = document.getElementById('error');
        const wallpaperContainer = document.getElementById('wallpaperContainer');
        const paginationContainer = document.getElementById('pagination');
        
        // 壁纸数据
        let wallpapers = [];
        let currentPage = 1;
        const itemsPerPage = 10;
        
        // 获取壁纸数据
        function fetchWallpapers() {
            // 显示加载中
            loadingElement.style.display = 'block';
            errorElement.style.display = 'none';
            
            // 发起API请求
            fetch(API_URL)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    // 隐藏加载中
                    loadingElement.style.display = 'none';
                    
                    // 处理数据
                    if (data && data.data && Array.isArray(data.data)) {
                        wallpapers = data.data;
                        currentPage = 1;
                        renderWallpapers();
                        renderPagination();
                    } else {
                        throw new Error('数据格式不正确');
                    }
                })
                .catch(error => {
                    // 显示错误信息
                    loadingElement.style.display = 'none';
                    errorElement.style.display = 'block';
                    errorElement.textContent = `加载失败: ${error.message}`;
                    console.error('获取壁纸数据出错:', error);
                });
        }
        
        // 渲染壁纸
        function renderWallpapers() {
            // 清空容器
            wallpaperContainer.innerHTML = '';
            
            // 计算当前页的壁纸
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, wallpapers.length);
            const currentWallpapers = wallpapers.slice(startIndex, endIndex);
            
            // 创建壁纸卡片
            currentWallpapers.forEach(wallpaper => {
                // 创建壁纸卡片元素
                const card = document.createElement('div');
                card.className = 'wallpaper-card';
                
                // 提取标签
                let tags = [];
                if (wallpaper.utag) {
                    tags = wallpaper.utag.split(' ').filter(tag => tag.trim() !== '');
                }
                
                // 构建HTML
                card.innerHTML = `
                    <img class="wallpaper-image" src="${wallpaper.url_thumb || wallpaper.url}" alt="壁纸预览" loading="lazy">
                    <div class="wallpaper-info">
                        <div class="wallpaper-resolution">
                            <a href="${wallpaper.url}" class="download-link no-link" target="_blank" data-url="${wallpaper.url}" title="点击复制地址">分辨率: ${wallpaper.resolution || '未知'}</a>
                        </div>
                        <div class="wallpaper-tags">
                            ${tags.map(tag => `<span class="wallpaper-tag">${tag}</span>`).join('')}
                        </div>
                        <div class="download-links">
                            <a href="${wallpaper.img_1920_1080 || wallpaper.img_1600_900 || wallpaper.url}" class="download-link" target="_blank" data-url="${wallpaper.img_1920_1080 || wallpaper.img_1600_900 || wallpaper.url}">1920x1080</a>
                            <a href="${wallpaper.img_1366_768 || wallpaper.url}" class="download-link" target="_blank" data-url="${wallpaper.img_1366_768 || wallpaper.url}">1366x768</a>
                            <a href="${wallpaper.img_1280_800 || wallpaper.url}" class="download-link" target="_blank" data-url="${wallpaper.img_1280_800 || wallpaper.url}">1280x800</a>
                        </div>
                    </div>
                `;
                
                // 添加到容器
                wallpaperContainer.appendChild(card);
            });
        }
        
        // 渲染分页
        function renderPagination() {
            // 清空分页容器
            paginationContainer.innerHTML = '';
            
            // 计算总页数
            const totalPages = Math.ceil(wallpapers.length / itemsPerPage);
            
            // 如果只有一页，不显示分页
            if (totalPages <= 1) {
                return;
            }
            
            // 创建分页按钮
            for (let i = 1; i <= totalPages; i++) {
                const button = document.createElement('button');
                button.textContent = i;
                button.className = i === currentPage ? 'active' : '';
                button.addEventListener('click', () => {
                    currentPage = i;
                    renderWallpapers();
                    renderPagination();
                    // 滚动到顶部
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
                paginationContainer.appendChild(button);
            }
        }
        
        // 为所有下载链接添加点击事件，复制URL到剪贴板
        function setupClipboardCopy() {
            // 使用事件委托，监听壁纸容器的点击事件
            wallpaperContainer.addEventListener('click', function(event) {
                // 检查点击的是否是下载链接
                if (event.target.classList.contains('download-link')) {
                    // 阻止默认行为（打开新窗口）
                    event.preventDefault();
                    
                    // 获取链接的URL
                    const url = event.target.getAttribute('data-url');
                    
                    // 复制到剪贴板
                    navigator.clipboard.writeText(url)
                        .then(() => {
                            // 创建右下角弹窗提示
                            const notification = document.createElement('div');
                            notification.textContent = '地址已经复制到剪贴板';
                            notification.style.position = 'fixed';
                            notification.style.bottom = '20px';
                            notification.style.right = '20px';
                            notification.style.backgroundColor = '#2196F3';
                            notification.style.color = 'white';
                            notification.style.padding = '10px 15px';
                            notification.style.borderRadius = '4px';
                            notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                            notification.style.zIndex = '1000';
                            notification.style.transition = 'opacity 0.5s';
                            
                            // 添加到页面
                            document.body.appendChild(notification);
                            
                            // 2秒后淡出并移除
                            setTimeout(() => {
                                notification.style.opacity = '0';
                                setTimeout(() => {
                                    document.body.removeChild(notification);
                                }, 500);
                            }, 2000);
                        })
                        .catch(err => {
                            console.error('复制到剪贴板失败:', err);
                        });
                }
            });
        }
        
                // 图片预览功能
                function setupLightbox() {
            // 获取DOM元素
            const lightboxOverlay = document.getElementById('lightboxOverlay');
            const lightboxImage = document.getElementById('lightboxImage');
            const closeButton = document.getElementById('closeButton');
            
            // 使用事件委托，监听壁纸容器的点击事件
            wallpaperContainer.addEventListener('click', function(event) {
                // 检查点击的是否是壁纸图片
                if (event.target.classList.contains('wallpaper-image')) {
                    // 获取原始图片URL（使用高清图片而不是缩略图）
                    const wallpaperCard = event.target.closest('.wallpaper-card');
                    const downloadLinks = wallpaperCard.querySelectorAll('.download-link');
                    // 使用第一个下载链接（原始尺寸）的URL
                    const imageUrl = downloadLinks[0].getAttribute('data-url');
                    
                    // 设置预览图片的URL
                    lightboxImage.src = imageUrl;
                    
                    // 显示预览
                    lightboxOverlay.classList.add('active');
                    
                    // 阻止事件冒泡
                    event.stopPropagation();
                }
            });
            
            // 点击关闭按钮关闭预览
            closeButton.addEventListener('click', function() {
                lightboxOverlay.classList.remove('active');
            });
            
            // 点击预览区域的空白处关闭预览
            lightboxOverlay.addEventListener('click', function(event) {
                // 如果点击的是预览区域本身（而不是图片），则关闭预览
                if (event.target === lightboxOverlay) {
                    lightboxOverlay.classList.remove('active');
                }
            });
            
            // 阻止点击图片时关闭预览
            lightboxImage.addEventListener('click', function(event) {
                event.stopPropagation();
            });
            
            // 按ESC键关闭预览
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape' && lightboxOverlay.classList.contains('active')) {
                    lightboxOverlay.classList.remove('active');
                }
            });
        }
        
        // 页面加载完成后自动获取壁纸数据
        document.addEventListener('DOMContentLoaded', () => {
            fetchWallpapers();
            setupClipboardCopy();
            setupLightbox();
        });
        
    </script>
</body>
</html>
