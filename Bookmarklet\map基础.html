<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="keywords" content="百度地图,百度地图API，百度地图自定义工具，百度地图所见即所得工具" />
    <meta name="description" content="百度地图API自定义地图，帮助用户在可视化操作下生成百度地图" />
    <title>百度地图API自定义地图</title>
    <!--引用百度地图API-->
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=j7cQnNNo6gupeurXWTcQiKRJHZkyHPVF&type=webgl"></script>
  </head>
  
  <body>
    <!--百度地图容器-->
    <div style="width:700px;height:550px;border:#ccc solid 1px;font-size:12px" id="map"></div>
  </body>
  <script type="text/javascript">
    //创建和初始化地图函数：
    function initMap(){
      createMap();//创建地图
      setMapEvent();//设置地图事件
      addMapControl();//向地图添加控件
      addMapOverlay();//向地图添加覆盖物
    }
    function createMap(){ 
      map = new BMapGL.Map("map"); 
      // 将地图中心点设置为标注点的位置，确保标注在视野内
      map.centerAndZoom(new BMapGL.Point(104.062204, 30.610723), 18);
      // 设置3D视角，降低倾斜角度使标注更容易看到
      map.setHeading(45);   // 减小地图旋转角度
      map.setTilt(35);      // 减小地图的倾斜角度
    }
    function setMapEvent(){
      map.enableScrollWheelZoom();
      map.enableKeyboard();
      map.enableDragging();
      map.enableDoubleClickZoom()
    }
    function addClickHandler(target,window){
      target.addEventListener("click",function(){
        target.openInfoWindow(window);
      });
    }
    function addMapOverlay(){
      var markers = [
        {content:"我的备注",title:"成都百都科技有限公司",imageOffset: {width:0,height:3},position:{lat:30.610723,lng:104.062204}}
      ];
      for(var index = 0; index < markers.length; index++ ){
        var point = new BMapGL.Point(markers[index].position.lng,markers[index].position.lat);
        
        // 使用3D立方体标记，调整高度和样式
        var options = {
          size: 30,           // 增大立方体大小
          shape: 'BMAP_POINT_SHAPE_CIRCLE',
          fillColor: '#FF0000',
          fillOpacity: 1.0,   // 完全不透明
          strokeColor: '#FFFFFF',  // 添加白色边框
          strokeWeight: 3,
          height: 50,          // 设置3D标记的高度，使其突出地面
          zIndex: 1000         // 添加zIndex属性，调整标注的层级
        };
        var marker = new BMapGL.Marker3D(point, options);
        
        // 创建标签，调整3D视角下的位置
        var labelOpts = {
          position: point,
          offset: new BMapGL.Size(0, -60),   // 调整偏移量，向上移动更多
          enableMassClear: true,
          styles: {
            color: '#fff',
            backgroundColor: '#FF0000',
            border: '3px solid #fff',
            padding: '8px 15px',
            borderRadius: '8px',
            fontSize: '16px',     // 增大字体
            fontWeight: 'bold',
            boxShadow: '0 4px 12px rgba(0,0,0,0.5)',
            zIndex: 9999        // 确保标签在最上层
          }
        };
        var label = new BMapGL.Label(markers[index].title, labelOpts);
        
        // 创建信息窗口
        var opts = {
          width: 250,
          title: markers[index].title,
          enableMessage: false
        };
        var infoWindow = new BMapGL.InfoWindow(markers[index].content, opts);
        
        // 添加点击事件
        addClickHandler(marker, infoWindow);
        
        // 添加覆盖物到地图
        map.addOverlay(marker);
        map.addOverlay(label);
        
        // 默认打开信息窗口
        marker.openInfoWindow(infoWindow);
      };
    }
    //向地图添加控件
    function addMapControl(){
      var scaleControl = new BMapGL.ScaleControl({anchor:BMAP_ANCHOR_BOTTOM_LEFT});
      scaleControl.setUnit(BMAP_UNIT_IMPERIAL);
      map.addControl(scaleControl);
      var navControl = new BMapGL.NavigationControl3D({anchor:BMAP_ANCHOR_TOP_LEFT});
      map.addControl(navControl);
      var overviewControl = new BMapGL.OverviewMapControl({anchor:BMAP_ANCHOR_BOTTOM_RIGHT,isOpen:true});
      map.addControl(overviewControl);
      // 添加地图类型控件
      var mapTypeControl = new BMapGL.MapTypeControl({anchor:BMAP_ANCHOR_TOP_RIGHT});
      map.addControl(mapTypeControl);
    }
    var map;
      initMap();
  </script>
</html>