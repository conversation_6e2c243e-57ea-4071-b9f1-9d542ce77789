<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS3按钮库演示</title>
    <style>
    /* 全局样式 */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #121212;
        color: #f5f5f5;
        line-height: 1.6;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 20px;
        background: linear-gradient(135deg, #121212 0%, #1e1e1e 100%);
    }

    .container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 40px;
        background-color: rgba(30, 30, 30, 0.8);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(10px);
    }

    h1 {
        text-align: center;
        margin-bottom: 40px;
        font-size: 2.5rem;
        color: #f5f5f5;
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        letter-spacing: 1px;
    }

    h2 {
        margin: 20px 0;
        font-size: 1.5rem;
        color: #ccc;
        border-bottom: 1px solid #444;
        padding-bottom: 10px;
    }

    .button-section {
        margin-bottom: 40px;
    }

    /* 基础按钮样式 */
    .btn {
        display: inline-block;
        padding: 12px 24px;
        margin: 10px;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        outline: none;
        position: relative;
        overflow: hidden;
        color: white;
        letter-spacing: 1px;
    }

    .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 7px 14px rgba(0, 0, 0, 0.3);
    }

    .btn:active {
        transform: translateY(1px);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
    }

    /* 基础颜色按钮 */
    .primary {
        background-color: #4361ee;
    }

    .primary:hover {
        background-color: #3a56d4;
    }

    .secondary {
        background-color: #6c757d;
    }

    .secondary:hover {
        background-color: #5a6268;
    }

    .success {
        background-color: #2ecc71;
    }

    .success:hover {
        background-color: #27ae60;
    }

    .danger {
        background-color: #e74c3c;
    }

    .danger:hover {
        background-color: #c0392b;
    }

    /* 渐变按钮 */
    .gradient-primary {
        background: linear-gradient(45deg, #4361ee, #3a0ca3);
        background-size: 200% auto;
        transition: 0.5s;
    }

    .gradient-primary:hover {
        background-position: right center;
    }

    .gradient-purple {
        background: linear-gradient(45deg, #8e2de2, #4a00e0);
        background-size: 200% auto;
        transition: 0.5s;
    }

    .gradient-purple:hover {
        background-position: right center;
    }

    .gradient-ocean {
        background: linear-gradient(45deg, #2193b0, #6dd5ed);
        background-size: 200% auto;
        transition: 0.5s;
    }

    .gradient-ocean:hover {
        background-position: right center;
    }

    /* 特效按钮 */
    .glow {
        background-color: #ff6b6b;
        box-shadow: 0 0 10px #ff6b6b, 0 0 20px #ff6b6b, 0 0 30px #ff6b6b;
        animation: glow 1.5s infinite alternate;
    }

    @keyframes glow {
        from {
            box-shadow: 0 0 10px #ff6b6b, 0 0 20px #ff6b6b;
        }
        to {
            box-shadow: 0 0 20px #ff6b6b, 0 0 30px #ff6b6b, 0 0 40px #ff6b6b;
        }
    }

    .pulse {
        background-color: #4cc9f0;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }

    .ripple {
        background-color: #7209b7;
        position: relative;
        overflow: hidden;
    }

    .ripple::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .ripple:hover::after {
        width: 300px;
        height: 300px;
    }

    .neon {
        background-color: transparent;
        border: 2px solid #08f7fe;
        color: #08f7fe;
        text-shadow: 0 0 5px #08f7fe, 0 0 10px #08f7fe;
        box-shadow: 0 0 5px #08f7fe, 0 0 10px #08f7fe, inset 0 0 5px #08f7fe;
        transition: all 0.3s;
    }

    .neon:hover {
        background-color: #08f7fe;
        color: #000;
        text-shadow: none;
    }

    /* 轮廓按钮 */
    .outline-primary {
        background-color: transparent;
        border: 2px solid #4361ee;
        color: #4361ee;
        transition: all 0.3s;
    }

    .outline-primary:hover {
        background-color: #4361ee;
        color: white;
    }

    .outline-success {
        background-color: transparent;
        border: 2px solid #2ecc71;
        color: #2ecc71;
        transition: all 0.3s;
    }

    .outline-success:hover {
        background-color: #2ecc71;
        color: white;
    }

    .outline-danger {
        background-color: transparent;
        border: 2px solid #e74c3c;
        color: #e74c3c;
        transition: all 0.3s;
    }

    .outline-danger:hover {
        background-color: #e74c3c;
        color: white;
    }

    /* 3D按钮 */
    .btn-3d-1 {
        background-color: #f39c12;
        box-shadow: 0 6px 0 #d35400;
        transform: translateY(0);
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .btn-3d-1:hover {
        transform: translateY(-3px);
        box-shadow: 0 9px 0 #d35400;
    }

    .btn-3d-1:active {
        transform: translateY(3px);
        box-shadow: 0 3px 0 #d35400;
    }

    .btn-3d-2 {
        background-color: #3498db;
        box-shadow: 0 6px 0 #2980b9;
        transform: translateY(0);
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .btn-3d-2:hover {
        transform: translateY(-3px);
        box-shadow: 0 9px 0 #2980b9;
    }

    .btn-3d-2:active {
        transform: translateY(3px);
        box-shadow: 0 3px 0 #2980b9;
    }

    .btn-3d-3 {
        background-color: #9b59b6;
        box-shadow: 0 6px 0 #8e44ad;
        transform: translateY(0);
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .btn-3d-3:hover {
        transform: translateY(-3px);
        box-shadow: 0 9px 0 #8e44ad;
    }

    .btn-3d-3:active {
        transform: translateY(3px);
        box-shadow: 0 3px 0 #8e44ad;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .container {
            padding: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            font-size: 14px;
            margin: 5px;
        }
        
        h1 {
            font-size: 2rem;
        }
        
        h2 {
            font-size: 1.3rem;
        }
    }
    </style>
</head>
<body>
    <div class="container">
        <h1>CSS3按钮库演示</h1>
        
        <section class="button-section">
            <h2>基础按钮</h2>
            <button class="btn primary">主要按钮</button>
            <button class="btn secondary">次要按钮</button>
            <button class="btn success">成功按钮</button>
            <button class="btn danger">危险按钮</button>
        </section>

        <section class="button-section">
            <h2>渐变按钮</h2>
            <button class="btn gradient-primary">渐变主要</button>
            <button class="btn gradient-purple">渐变紫色</button>
            <button class="btn gradient-ocean">渐变海洋</button>
        </section>

        <section class="button-section">
            <h2>特效按钮</h2>
            <button class="btn glow">发光按钮</button>
            <button class="btn pulse">脉冲按钮</button>
            <button class="btn ripple">波纹按钮</button>
            <button class="btn neon">霓虹按钮</button>
        </section>

        <section class="button-section">
            <h2>轮廓按钮</h2>
            <button class="btn outline-primary">轮廓主要</button>
            <button class="btn outline-success">轮廓成功</button>
            <button class="btn outline-danger">轮廓危险</button>
        </section>

        <section class="button-section">
            <h2>3D按钮</h2>
            <button class="btn btn-3d-1">3D按钮 1</button>
            <button class="btn btn-3d-2">3D按钮 2</button>
            <button class="btn btn-3d-3">3D按钮 3</button>
        </section>
    </div>
</body>
</html>