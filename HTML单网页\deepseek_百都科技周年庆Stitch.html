<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百都科技20周年庆典 - 成都百都科技有限公司</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary: #0F2C59;
            --secondary: #FFD700;
            --accent: #E63946;
            --light: #F8F9FA;
            --dark: #212529;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            color: var(--dark);
            line-height: 1.6;
            background-color: #fff;
            overflow-x: hidden;
        }
        
        h1, h2, h3 {
            font-family: 'Playfair Display', serif;
            font-weight: 700;
        }
        
        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 顶部导航栏 */
        .navbar {
            background-color: rgba(15, 44, 89, 0.95);
            color: white;
            padding: 15px 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
        }
        
        .logo img {
            height: 50px;
            margin-right: 10px;
        }
        
        .logo-text {
            font-size: 24px;
            font-weight: 700;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
        }
        
        .nav-links li {
            margin-left: 30px;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-links a:hover {
            color: var(--secondary);
        }
        
        /* 英雄区域 */
        .hero {
            height: 100vh;
            background: linear-gradient(135deg, rgba(15, 44, 89, 0.9) 0%, rgba(15, 44, 89, 0.7) 100%), url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%230F2C59"/><path d="M0 50 L100 50" stroke="%23FFD700" stroke-width="0.5"/></svg>');
            background-size: cover;
            display: flex;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-content {
            width: 100%;
            padding-top: 70px;
            position: relative;
            z-index: 2;
        }
        
        .hero h1 {
            font-size: 4rem;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .hero h1 span {
            color: var(--secondary);
        }
        
        .hero p {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            max-width: 800px;
            margin: 0 auto 30px;
        }
        
        .hero-badge {
            background: var(--secondary);
            color: var(--primary);
            font-weight: bold;
            padding: 15px 30px;
            border-radius: 50px;
            display: inline-block;
            font-size: 1.2rem;
            margin-top: 20px;
            animation: pulse 2s infinite;
        }
        
        .celebration {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
            z-index: 1;
        }
        
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: var(--secondary);
            opacity: 0.8;
            animation: fall linear forwards;
        }
        
        /* 成就部分 */
        .achievements {
            padding: 100px 0;
            background-color: var(--light);
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .section-header h2 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
        }
        
        .section-header h2:after {
            content: '';
            position: absolute;
            width: 70px;
            height: 4px;
            background: var(--secondary);
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .section-header p {
            color: #666;
            max-width: 700px;
            margin: 20px auto 0;
        }
        
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-10px);
        }
        
        .stat-icon {
            font-size: 3rem;
            color: var(--secondary);
            margin-bottom: 20px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .stat-text {
            font-size: 1.1rem;
            color: #666;
        }
        
        /* 时间线部分 */
        .timeline-section {
            padding: 100px 0;
            background: linear-gradient(to bottom, #fff 0%, #f0f4f8 100%);
            position: relative;
        }
        
        .timeline {
            position: relative;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .timeline::after {
            content: '';
            position: absolute;
            width: 4px;
            background-color: var(--secondary);
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -2px;
        }
        
        .timeline-item {
            padding: 20px 40px;
            position: relative;
            width: 50%;
            box-sizing: border-box;
        }
        
        .timeline-item:nth-child(odd) {
            left: 0;
        }
        
        .timeline-item:nth-child(even) {
            left: 50%;
        }
        
        .timeline-content {
            padding: 30px;
            background-color: white;
            position: relative;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .timeline-year {
            position: absolute;
            top: 20px;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--secondary);
            background: var(--primary);
            padding: 5px 15px;
            border-radius: 30px;
        }
        
        .timeline-item:nth-child(odd) .timeline-year {
            right: -80px;
        }
        
        .timeline-item:nth-child(even) .timeline-year {
            left: -80px;
        }
        
        .timeline-content h3 {
            margin-bottom: 15px;
            color: var(--primary);
        }
        
        /* 服务展示 */
        .services {
            padding: 100px 0;
            background-color: var(--primary);
            color: white;
        }
        
        .services .section-header h2 {
            color: white;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 40px 30px;
            text-align: center;
            transition: transform 0.3s ease, background 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .service-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-10px);
        }
        
        .service-icon {
            font-size: 3.5rem;
            color: var(--secondary);
            margin-bottom: 25px;
        }
        
        .service-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        /* 公司理念 */
        .philosophy {
            padding: 100px 0;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect width="100" height="100" fill="%23f0f4f8"/></svg>');
        }
        
        .philosophy-content {
            display: flex;
            align-items: center;
            gap: 50px;
        }
        
        .quote-container {
            flex: 1;
            padding: 40px;
            background: var(--primary);
            border-radius: 15px;
            color: white;
            position: relative;
        }
        
        .quote-container::before {
            content: """;
            position: absolute;
            top: -30px;
            left: 20px;
            font-size: 10rem;
            font-family: Georgia, serif;
            color: rgba(255, 215, 0, 0.2);
            line-height: 1;
        }
        
        .quote {
            font-size: 2rem;
            font-style: italic;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }
        
        .quote-author {
            font-size: 1.2rem;
            text-align: right;
            color: var(--secondary);
        }
        
        .client-logos {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }
        
        .logo-item {
            background: white;
            border-radius: 10px;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: 15px;
            font-weight: bold;
            color: var(--primary);
        }
        
        /* 页脚 */
        .footer {
            background: var(--primary);
            color: white;
            padding: 80px 0 30px;
            position: relative;
            overflow: hidden;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }
        
        .footer-column h3 {
            font-size: 1.5rem;
            margin-bottom: 25px;
            color: var(--secondary);
        }
        
        .footer-column ul {
            list-style: none;
        }
        
        .footer-column ul li {
            margin-bottom: 15px;
        }
        
        .footer-column ul li a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-column ul li a:hover {
            color: var(--secondary);
        }
        
        .social-links {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .social-links a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            color: white;
            transition: background 0.3s, transform 0.3s;
        }
        
        .social-links a:hover {
            background: var(--secondary);
            transform: translateY(-5px);
            color: var(--primary);
        }
        
        .copyright {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
        }
        
        /* 动画 */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @keyframes fall {
            to { transform: translateY(100vh) rotate(360deg); }
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .hero h1 {
                font-size: 3rem;
            }
            
            .philosophy-content {
                flex-direction: column;
            }
            
            .timeline::after {
                left: 31px;
            }
            
            .timeline-item {
                width: 100%;
                padding-left: 70px;
                padding-right: 25px;
            }
            
            .timeline-item:nth-child(even) {
                left: 0;
            }
            
            .timeline-item:nth-child(odd) .timeline-year,
            .timeline-item:nth-child(even) .timeline-year {
                left: 15px;
                right: auto;
            }
        }
        
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .hero p {
                font-size: 1.2rem;
            }
            
            .section-header h2 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="container nav-container">
            <div class="logo">
                <div class="logo-text">百都科技</div>
            </div>
            <ul class="nav-links">
                <li><a href="#hero">首页</a></li>
                <li><a href="#achievements">成就</a></li>
                <li><a href="#timeline">发展历程</a></li>
                <li><a href="#services">服务</a></li>
                <li><a href="#philosophy">理念</a></li>
                <li><a href="#contact">联系我们</a></li>
            </ul>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section id="hero" class="hero">
        <div class="celebration" id="celebration"></div>
        <div class="container hero-content">
            <h1>辉煌<span>二十载</span> · 携手创未来</h1>
            <p>成都百都科技有限公司20周年庆典 - 致敬过去，展望未来</p>
            <div class="hero-badge">2003 - 2023</div>
        </div>
    </section>

    <!-- 成就部分 -->
    <section id="achievements" class="achievements">
        <div class="container">
            <div class="section-header">
                <h2>辉煌成就</h2>
                <p>二十年深耕，百都科技已成为中国西部领先的互联网营销服务商</p>
            </div>
            
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number" data-target="1000">1000+</div>
                    <div class="stat-text">员工人数</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <div class="stat-number" data-target="100">100+</div>
                    <div class="stat-text">覆盖行业</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-city"></i>
                    </div>
                    <div class="stat-number" data-target="21">21</div>
                    <div class="stat-text">覆盖四川地市州</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-number" data-target="20">20</div>
                    <div class="stat-text">年专业服务</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 时间线部分 -->
    <section id="timeline" class="timeline-section">
        <div class="container">
            <div class="section-header">
                <h2>发展历程</h2>
                <p>二十年风雨兼程，见证百都科技的成长与蜕变</p>
            </div>
            
            <div class="timeline">
                <div class="timeline-item">
                    <div class="timeline-year">2003</div>
                    <div class="timeline-content">
                        <h3>公司创立</h3>
                        <p>成都百都科技有限公司成立，成为百度在四川的重要合作伙伴，开启互联网营销服务新征程。</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2008</div>
                    <div class="timeline-content">
                        <h3>业务扩展</h3>
                        <p>业务覆盖四川主要城市，员工人数突破200人，服务超过1000家中小企业。</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2013</div>
                    <div class="timeline-content">
                        <h3>十年里程碑</h3>
                        <p>公司成立十周年，荣获"中国西部互联网营销服务领军企业"称号，业务范围进一步扩大。</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2018</div>
                    <div class="timeline-content">
                        <h3>多元化发展</h3>
                        <p>新增信息流推广、本地广告推广、百度金融等业务，形成全方位互联网营销服务生态。</p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-year">2023</div>
                    <div class="timeline-content">
                        <h3>二十周年庆典</h3>
                        <p>员工人数超千人，业务覆盖四川所有地市州，成为西部最具影响力的互联网营销服务商。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 服务展示 -->
    <section id="services" class="services">
        <div class="container">
            <div class="section-header">
                <h2>专业服务</h2>
                <p>全方位、深层次的互联网营销解决方案</p>
            </div>
            
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>百度搜索营销</h3>
                    <p>精准定位目标客户，提升品牌曝光度，实现高效转化</p>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-stream"></i>
                    </div>
                    <h3>信息流推广</h3>
                    <p>原生广告形式，在用户浏览信息时自然呈现，提高用户接受度</p>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h3>本地广告推广</h3>
                    <p>精准定位本地客户群体，提升区域品牌影响力</p>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <h3>百度金融服务</h3>
                    <p>为中小企业提供专业金融解决方案，助力业务发展</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 公司理念 -->
    <section id="philosophy" class="philosophy">
        <div class="container">
            <div class="section-header">
                <h2>核心理念</h2>
                <p>客户成功，就是我们成功</p>
            </div>
            
            <div class="philosophy-content">
                <div class="quote-container">
                    <div class="quote">
                        "二十年来，我们始终坚信客户的成长就是我们的成功。通过专业的互联网营销服务，我们帮助四川中小企业实现数字化转型，推动区域经济发展。"
                    </div>
                    <div class="quote-author">—— 百都科技管理团队</div>
                </div>
                
                <div class="client-logos">
                    <div class="logo-item">合作企业</div>
                    <div class="logo-item">合作伙伴</div>
                    <div class="logo-item">行业客户</div>
                    <div class="logo-item">战略联盟</div>
                    <div class="logo-item">政府机构</div>
                    <div class="logo-item">金融机构</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3>百都科技</h3>
                    <p>成都百都科技有限公司（暨百度营销四川服务中心）是百度在川合作伙伴，致力于为中国各行业用户提供全方位、深层次的互联网营销服务。</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-weixin"></i></a>
                        <a href="#"><i class="fab fa-weibo"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                    </div>
                </div>
                
                <div class="footer-column">
                    <h3>联系方式</h3>
                    <ul>
                        <li><i class="fas fa-map-marker-alt"></i> 四川省成都市高新区天府大道</li>
                        <li><i class="fas fa-phone"></i> 028-XXXXXXX</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                        <li><i class="fas fa-clock"></i> 工作日 9:00 - 18:00</li>
                    </ul>
                </div>
                
                <div class="footer-column">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="#hero">首页</a></li>
                        <li><a href="#achievements">公司成就</a></li>
                        <li><a href="#timeline">发展历程</a></li>
                        <li><a href="#services">服务项目</a></li>
                        <li><a href="#philosophy">企业理念</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="copyright">
                <p>© 2023 成都百都科技有限公司 版权所有 | 二十周年庆典</p>
            </div>
        </div>
    </footer>

    <script>
        // 创建庆祝彩带效果
        function createConfetti() {
            const container = document.getElementById('celebration');
            const colors = ['#FFD700', '#E63946', '#FFFFFF', '#0F2C59'];
            
            for(let i = 0; i < 150; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.width = Math.random() * 10 + 5 + 'px';
                confetti.style.height = Math.random() * 10 + 5 + 'px';
                confetti.style.animationDuration = Math.random() * 3 + 5 + 's';
                confetti.style.animationDelay = Math.random() * 5 + 's';
                container.appendChild(confetti);
            }
        }
        
        // 数字动画效果
        function animateValue(obj, start, end, duration) {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                obj.innerHTML = Math.floor(progress * (end - start) + start) + "+";
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        }
        
        // 滚动动画
        function animateOnScroll() {
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(number => {
                const target = parseInt(number.getAttribute('data-target'));
                animateValue(number, 0, target, 2000);
            });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createConfetti();
            
            // 滚动监听
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        animateOnScroll();
                        observer.disconnect();
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(document.querySelector('.achievements'));
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        });
    </script>
</body>
</html>