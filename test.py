from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import numpy as np
import os
from scipy import ndimage
import matplotlib.pyplot as plt

class SimpleCaptchaProcessor:
    def __init__(self):
        pass
    
    def load_image(self, image_path):
        """加载图片"""
        try:
            return Image.open(image_path)
        except Exception as e:
            print(f"加载图片失败: {e}")
            return None
    
    def convert_to_grayscale(self, image):
        """转换为灰度图"""
        if image.mode != 'L':
            return image.convert('L')
        return image
    
    def enhance_contrast(self, image, factor=2.0):
        """增强对比度"""
        enhancer = ImageEnhance.Contrast(image)
        return enhancer.enhance(factor)
    
    def enhance_sharpness(self, image, factor=2.0):
        """增强锐度"""
        enhancer = ImageEnhance.Sharpness(image)
        return enhancer.enhance(factor)
    
    def apply_filters(self, image):
        """应用滤镜去噪"""
        # 高斯模糊去噪
        image = image.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        # 中值滤波去除椒盐噪声
        image = image.filter(ImageFilter.MedianFilter(size=3))
        
        # 锐化滤镜
        image = image.filter(ImageFilter.SHARPEN)
        
        return image
    
    def apply_threshold(self, image, threshold=128):
        """二值化处理"""
        # 转换为numpy数组
        img_array = np.array(image)
        
        # 应用阈值
        binary_array = np.where(img_array > threshold, 255, 0).astype(np.uint8)
        
        # 转换回PIL图像
        return Image.fromarray(binary_array, mode='L')
    
    def auto_threshold(self, image):
        """自动阈值（类似OTSU）"""
        img_array = np.array(image)
        
        # 计算直方图
        hist, bins = np.histogram(img_array.flatten(), 256, [0, 256])
        
        # 简单的双峰检测来找到阈值
        total_pixels = img_array.size
        sum_total = np.sum(np.arange(256) * hist)
        
        sum_background = 0
        weight_background = 0
        max_variance = 0
        threshold = 0
        
        for i in range(256):
            weight_background += hist[i]
            if weight_background == 0:
                continue
                
            weight_foreground = total_pixels - weight_background
            if weight_foreground == 0:
                break
                
            sum_background += i * hist[i]
            mean_background = sum_background / weight_background
            mean_foreground = (sum_total - sum_background) / weight_foreground
            
            # 计算类间方差
            variance = weight_background * weight_foreground * (mean_background - mean_foreground) ** 2
            
            if variance > max_variance:
                max_variance = variance
                threshold = i
        
        return self.apply_threshold(image, threshold)
    
    def remove_noise_morphology(self, image):
        """使用形态学操作去噪"""
        img_array = np.array(image)
        
        # 创建结构元素
        kernel = np.ones((2, 2), np.uint8)
        
        # 开运算：去除小噪点
        opened = ndimage.binary_opening(img_array, structure=kernel)
        
        # 闭运算：填充字符内部的洞
        closed = ndimage.binary_closing(opened, structure=kernel)
        
        # 转换回0-255范围
        result = (closed * 255).astype(np.uint8)
        
        return Image.fromarray(result, mode='L')
    
    def resize_image(self, image, scale_factor=3):
        """放大图片"""
        width, height = image.size
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        # 使用高质量重采样
        return image.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    def preprocess_image(self, image_path, show_steps=False):
        """完整的预处理流程"""
        # 加载图片
        original = self.load_image(image_path)
        if original is None:
            return None
        
        if show_steps:
            steps = [("原图", original.copy())]
        
        # 1. 转换为灰度图
        processed = self.convert_to_grayscale(original)
        if show_steps:
            steps.append(("灰度图", processed.copy()))
        
        # 2. 增强对比度
        processed = self.enhance_contrast(processed, factor=1.5)
        if show_steps:
            steps.append(("增强对比度", processed.copy()))
        
        # 3. 应用滤镜去噪
        processed = self.apply_filters(processed)
        if show_steps:
            steps.append(("滤镜去噪", processed.copy()))
        
        # 4. 增强锐度
        processed = self.enhance_sharpness(processed, factor=1.5)
        if show_steps:
            steps.append(("增强锐度", processed.copy()))
        
        # 5. 自动阈值二值化
        processed = self.auto_threshold(processed)
        if show_steps:
            steps.append(("二值化", processed.copy()))
        
        # 6. 形态学去噪
        processed = self.remove_noise_morphology(processed)
        if show_steps:
            steps.append(("形态学去噪", processed.copy()))
        
        # 7. 放大图片
        processed = self.resize_image(processed, scale_factor=3)
        if show_steps:
            steps.append(("放大图片", processed.copy()))
        
        if show_steps:
            self.show_processing_steps(steps)
        
        return processed
    
    def show_processing_steps(self, steps):
        """显示处理步骤"""
        n_steps = len(steps)
        cols = 3
        rows = (n_steps + cols - 1) // cols
        
        plt.figure(figsize=(15, 5 * rows))
        
        for i, (title, image) in enumerate(steps):
            plt.subplot(rows, cols, i + 1)
            if image.mode == 'RGB':
                plt.imshow(image)
            else:
                plt.imshow(image, cmap='gray')
            plt.title(title)
            plt.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    def save_processed_image(self, processed_image, output_path):
        """保存处理后的图片"""
        try:
            processed_image.save(output_path)
            print(f"处理后的图片已保存至: {output_path}")
            return True
        except Exception as e:
            print(f"保存图片错误: {e}")
            return False
    
    def process_image(self, image_path, output_path=None, show_steps=False):
        """完整的图片预处理流程"""
        try:
            processed_image = self.preprocess_image(image_path, show_steps)
            
            if processed_image is None:
                return None
            
            if output_path:
                self.save_processed_image(processed_image, output_path)
            
            return processed_image
        except Exception as e:
            print(f"处理错误: {e}")
            return None
    
    def batch_process(self, image_folder, output_folder):
        """批量处理图片"""
        if not os.path.exists(image_folder):
            print(f"输入文件夹不存在: {image_folder}")
            return []
        
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            print(f"创建输出文件夹: {output_folder}")
        
        processed_files = []
        image_files = [f for f in os.listdir(image_folder) 
                      if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
        
        print(f"找到 {len(image_files)} 张图片待处理")
        
        for i, filename in enumerate(image_files, 1):
            input_path = os.path.join(image_folder, filename)
            output_filename = f"processed_{filename}"
            output_path = os.path.join(output_folder, output_filename)
            
            print(f"[{i}/{len(image_files)}] 处理图片: {filename}")
            
            processed_image = self.process_image(input_path, output_path)
            
            if processed_image is not None:
                processed_files.append({
                    'input_file': filename,
                    'output_file': output_filename,
                    'status': 'success'
                })
                print(f"✓ 处理成功")
            else:
                processed_files.append({
                    'input_file': filename,
                    'output_file': output_filename,
                    'status': 'failed'
                })
                print(f"✗ 处理失败")
            
            print("-" * 50)
        
        success_count = sum(1 for f in processed_files if f['status'] == 'success')
        print(f"批量处理完成: {success_count}/{len(image_files)} 张图片处理成功")
        
        return processed_files

# 使用示例
def main():
    processor = SimpleCaptchaProcessor()
    
    # 单张图片处理
    input_image = "captcha.png"
    output_image = "processed_captcha.png"
    
    if os.path.exists(input_image):
        print("开始处理验证码图片...")
        processed_image = processor.process_image(input_image, output_image, show_steps=True)
        
        if processed_image is not None:
            print("✓ 图片处理完成")
        else:
            print("✗ 图片处理失败")
    else:
        print(f"图片文件不存在: {input_image}")
    
    # 批量处理
    input_folder = "captcha_images"
    output_folder = "processed_images"
    
    if os.path.exists(input_folder):
        results = processor.batch_process(input_folder, output_folder)

if __name__ == "__main__":
    main()