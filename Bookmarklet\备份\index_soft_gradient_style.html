<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小书签驿站-Your Bookmarklet Station</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html, body {
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            padding: 20px;
            max-width: 1400px; /* 调整最大宽度 */
            margin: 0 auto;
            background: linear-gradient(135deg, #a1c4fd 0%, #c2e9fb 100%); /* 柔和蓝紫渐变背景 */
            background-attachment: fixed;
            color: #333; /* 深灰色文字 */
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        h1 {
            text-align: center;
            color: #2c3e50; /* 深蓝色标题 */
            margin-bottom: 10px;
             text-shadow: 1px 1px 2px rgba(0,0,0,0.1); /* 轻微文字阴影 */
        }
        h4 {
            text-align: center;
            margin-bottom: 30px;
            color: #7f8c8d; /* 偏淡的灰色副标题 */
            margin-top: -10px; /* 调整间距 */
            font-weight: normal;
        }
        .zsline{
            width: 250px; /* 调整线条宽度 */
            margin: 0 auto 20px;
        }
         .zsline svg path {
            stroke: #bdc3c7; /* 浅灰色线条 */
             stroke-width: 5;
        }
        .tags-container {
            margin-bottom: 20px;
            text-align: center;
             padding: 12px 0; /* 增加内边距 */
             background-color: rgba(255,255,255,0.8); /* 半透明白色背景 */
             border-radius: 12px; /* 更圆润的圆角 */
             box-shadow: 0 4px 10px rgba(0,0,0,0.08); /* 柔和阴影 */
        }
        .tag {
            display: inline-block;
            padding: 8px 20px;
            margin: 5px;
            background: #e0e0e0; /* 浅灰色背景 */
            border: 1px solid #d0d0d0; /* 边框颜色 */
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateY(0);
            position: relative;
            text-indent: 0;
            color: #333;
            font-size: 15px;
             box-shadow: 0 1px 3px rgba(0,0,0,0.05); /* 轻微阴影 */
        }
        .tags-container .tag:nth-child(1){
            text-indent:0;
        }
        .tag:hover {
            transform: translateY(-3px); /* 调整悬停位移 */
            box-shadow: 0 6px 12px rgba(0,0,0,0.1); /* 阴影增强 */
             border-color: #87ceeb; /* 悬停边框变浅蓝色 */
             color: #1e90ff; /* 悬停文字变蓝色 */
        }

        .tag.active {
            background: linear-gradient(45deg, #87ceeb 0%, #1e90ff 100%); /* 蓝色渐变背景 */
            color: white;
            border-color: #1e90ff;
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(30, 144, 255, 0.4); /* 蓝色阴影 */
        }
        .tag:not(.active):hover {
            border-color: #87ceeb;
            color: #1e90ff;
        }
         .tags-container .tag:nth-child(n+2)::after{
           content: none; /* 移除图标 */
        }
         .tag i {
           display: none; /* 隐藏图标 */
        }

        .bookmarklets-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px; /* 增加间距 */
            padding: 10px 0; /* 调整内边距 */
            width: 100%;
            box-sizing: border-box;
            justify-content: center;
            position: relative;
            z-index: 1;
        }

        .bookmarklet-card {
            border-radius: 12px; /* 调整圆角 */
            padding: 25px; /* 增加内边距 */
            box-shadow: 0 4px 12px rgba(0,0,0,0.08); /* 柔和阴影 */
            position: relative;
            transition: all 0.3s ease;
            transform: translateY(0);
            border: 1px solid #e0e0e0; /* 浅色边框 */
            background: #ffffff; /* 白色背景 */
            height: auto;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            margin: 0;
            width: 100%;
            color: #333;
             overflow: hidden;
        }

        .bookmarklet-card.loaded:hover  {
            transform: translateY(-5px); /* 调整悬停位移 */
            box-shadow: 0 8px 18px rgba(0,0,0,0.12);  /* 阴影增强 */
            border-color: #c2e9fb;  /* 悬停时边框变浅 */
            background: #ffffff;
        }
        .bookmark-icon {
            position: absolute;
            top: 20px; /* 调整位置 */
            right: 20px; /* 调整位置 */
            width: 22px; /* 调整图标大小 */
            height: 22px;
            cursor: pointer;
            color: #a0a0a0; /* 灰色图标 */
            transition: all 0.3s ease;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23a0a0a0'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15l-5-2.18L7 18V5h10v13z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.8; /* 调整透明度 */
            z-index: 2;
             filter: none;
        }

        .bookmark-icon:hover {
            opacity: 1;
            transform: scale(1.1);
             filter: none;
             background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e90ff'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15l-5-2.18L7 18V5h10v13z'/%3E%3C/svg%3E"); /* 悬停变蓝色 */
        }

        .bookmark-icon.active {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231e90ff'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z'/%3E%3Csvg%3E"); /* 激活变蓝色 */
            opacity: 1;
             filter: none;
        }
        .bookmarklet-title {
            display: block;
            width: fit-content;
            font-size: 18px;
            height: auto;
            line-height: 1.5;
            color: #2c3e50; /* 深蓝色标题 */
            cursor: pointer;
            text-overflow: ellipsis;
            white-space: normal;
            text-decoration: none;
             text-shadow: none;
            margin-bottom: 8px; /* 调整底部间距 */
        }
        .bookmarklet-card:hover .bookmarklet-title {
            color: #1e90ff; /* 悬停标题变蓝色 */
             text-shadow: none;
        }
        .external-svg{
            width: 15px; /* 调整图标大小 */
            height: 15px;
            margin-top: 1px;
            margin-left: 4px;
            color: #a0a0a0; /* 外部链接图标颜色 */
            display: inline-block;
            vertical-align: middle;
            opacity: 0.7;
        }
        .bookmarklet-card:hover .external-svg{
            display: inline-block;
             color: #1e90ff; /* 悬停变蓝色 */
        }
        .bookmarklet-description {
            height: auto;
            line-height: 1.6;
            font-size: 14px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            margin: 8px 0 12px; /* 调整间距 */
            flex-shrink: 0;
            color: #555; /* 描述文字颜色 */
        }
        .bookmarklet-card.active-card .bookmarklet-description {
            filter: none;
            opacity: 1;
        }

        .bookmarklet-tags {
            margin-top: auto;
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            height: auto;
            overflow: hidden;
        }
        /* 添加来源样式 */
        .bookmarklet-source {
            position: static;
            margin-top: 15px; /* 增加顶部间距 */
            font-size: 13px; /* 调整字体大小 */
            color: #7f8c8d;
            display: flex;
            align-items: center;
            gap: 4px;
            text-decoration: none;
            transition: all 0.2s ease;
        }

        .bookmarklet-source:hover {
            color: #1e90ff; /* 悬停来源变蓝色 */
        }

        .bookmarklet-source svg {
            width: 13px; /* 调整图标大小 */
            height: 13px;
             fill: #7f8c8d;
             transition: fill 0.2s ease;
        }
         .bookmarklet-source:hover svg {
            fill: #1e90ff; /* 悬停来源图标变蓝色 */
         }

        /* 帮助按钮和返回顶部按钮的通用样式 */
        .help-icon,
        .back-to-top,
        .email-icon {
            width: 40px; /* 调整按钮大小 */
            height: 40px;
            background: linear-gradient(45deg, #87ceeb 0%, #1e90ff 100%); /* 蓝色渐变背景 */
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(30, 144, 255, 0.3); /* 蓝色阴影 */
            transition: all 0.3s ease;
            position: fixed;
            right: 25px; /* 调整位置 */
            z-index: 1000;
        }

        .help-icon {
            top: 25px; /* 调整位置 */
            font-size: 20px;
        }
        .email-icon {
            top: 80px; /* 调整位置 */
            background: linear-gradient(45deg, #ffb6c1 0%, #ff69b4 100%); /* 粉色渐变背景 */
             box-shadow: 0 4px 10px rgba(255, 105, 180, 0.4); /* 粉色阴影 */
        }
        .help-icon:hover {
            transform: scale(1.1);
             box-shadow: 0 6px 15px rgba(30, 144, 255, 0.5); /* 悬停阴影增强 */
        }
        .email-icon:hover {
            transform: scale(1.1);
             box-shadow: 0 6px 15px rgba(255, 105, 180, 0.6); /* 悬停阴影增强 */
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.4); /* 半透明黑色背景 */
            z-index: 1002;
        }

        .modal.show {
            display: block;
        }

        .modal-content {
            background: linear-gradient(135deg, #e0f2f7 0%, #b2ebf2 100%); /* 柔和蓝绿渐变背景 */
            padding: 30px;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 6px 20px rgba(0, 188, 212, 0.3); /* 蓝绿阴影 */
            z-index: 1003;
             border: none;
             color: #333;
        }

        .modal-content h2 {
            font-size: 20px;
            color: #00796b; /* 深青色标题 */
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
             text-shadow: none;
        }

        .modal-content p {
            margin: 10px 0;
            color: #555;
            font-size: 15px;
            line-height: 1.6;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-content p::before {
            content: '';
            width: 20px;
            height: 20px;
            flex-shrink: 0;
            background-size: contain;
            background-repeat: no-repeat;
             filter: none;
        }

        .modal-content p:nth-child(2)::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300796b'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E"); /* 青色图标 */
        }

        .modal-content p:nth-child(3)::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300796b'%3E%3Cpath d='M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1zM8 13h8v-2H8v2zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5z'/%3E%3C/svg%3E"); /* 青色图标 */
        }

        .modal-content p:nth-child(4)::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2300796b'%3E%3Cpath d='M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z'/%3E%3C/svg%3E"); /* 青色图标 */
        }

        .modal-close {
            position: absolute;
            right: 15px;
            top: 15px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #7f8c8d;
            font-size: 20px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: #e0e0e0; /* 悬停背景 */
            color: #333;
        }
        .bookmarklet-card.active-card {
            position: relative;
            z-index: 1004;
        }

        .bookmarklet-card.active-card .bookmarklet-title {
            position: relative;
            display: inline-block;
            border: 2px solid #ff8a65; /* 柔和橙色边框 */
            border-radius: 4px;
            background: #fff;
            animation: none;
            padding: 0 8px;
            margin: -4px -8px;
            height: auto;
            line-height: 1.5;
            color: #ff5722; /* 深橙色文字 */
        }

        @keyframes borderWidthPulse {
           /* 移除动画 */
        }

        /* 确保拖动提示也在正确的层级 */
        .drag-hint {
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            width: 30px;
            height: 30px;
            z-index: 1005;
        }

        /* 使用SVG箭头替代文字箭头，方向改为从右向左 */
        .drag-hint::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff5722'%3E%3Cpath d='M13 20v-8.17l3.59 3.58L18 14l-6-6-6 6 1.41 1.41L11 11.83V20h2z'/%3E%3C/svg%3E"); /* 橙色箭头 */
            background-size: contain;
            background-repeat: no-repeat;
             filter: none;
        }

        .drag-hint.show {
            opacity: 1;
            animation: dragAnimation 2s infinite;
        }

        @keyframes dragAnimation {
            0% { transform: translateY(5px) translateX(0px); opacity: 0.7; }
            50% { transform: translateY(0px) translateX(0px); opacity: 1; }
            100% { transform: translateY(5px) translateX(0px); opacity: 0.7; }
        }

        /* 调整卡片布局 */
        /* 书签栏提示 */
        .bookmark-bar-hint {
            position: fixed;
            top: 0;
            left: 50%;
            transform: translateX(-50%) translateY(-100%);
            background: #fff9c4; /* 浅黄色背景 */
            border: 1px solid #fff59d; /* 浅黄色边框 */
            padding: 8px 20px 8px 35px;
            text-align: center;
            color: #fbc02d; /* 深黄色文字 */
            font-weight: normal;
            border-radius: 0 0 8px 8px;
            transition: transform 0.3s;
            z-index: 99999;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
        }
        .bookmark-bar-hint span {
            color: #ff5722; /* 橙色文字 */
        }
        .bookmark-bar-hint::before {
            content: '⬆';
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            animation: upArrowBounce 1s infinite;
             color: #ff5722; /* 橙色箭头 */
        }

        @keyframes upArrowBounce {
            0%, 100% {
                transform: translateY(-50%);
            }
            50% {
                transform: translateY(-70%);
            }
        }

        .bookmark-bar-hint.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
            visibility: visible;
        }

        .bookmarklet-tag {
            display: inline-block;
            padding: 4px 10px; /* 调整内边距 */
            font-size: 12px;
            color: #555; /* 文字颜色 */
            background-color: #e0e0e0; /* 浅灰色背景 */
             border-radius: 4px;
            transition: all 0.2s ease;
             border: none;
        }

        .bookmarklet-tag:hover {
            background-color: #c2e9fb; /* 浅蓝色背景 */
            color: #333; /* 悬停文字 */
            transform: translateY(-1px);
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }
        /* 版权栏样式 */
        .footer {
            margin-top: auto;
            text-align: center;
            padding: 30px 0;
            color: #7f8c8d;
            font-size: 13px;
            border-top: 1px solid #e0e0e0; /* 浅色边框 */
             position: relative;
             z-index: 1;
             background: linear-gradient(135deg, #c2e9fb 0%, #a1c4fd 100%); /* 柔和蓝紫渐变背景 */
             color: #555;
        }

        .footer a {
            color: #555; /* 链接颜色 */
            text-decoration: none;
            transition: color 0.2s;
        }

        .footer a:hover {
            color: #1e90ff; /* 悬停变蓝色 */
            text-decoration: underline;
        }

        body::after {
            content: none; /* 移除背景流线 */
        }

        /* 移动端响应式处理 */
        @media screen and (max-width: 768px) {
            .bookmarklets-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .bookmarklet-card {
                width: 100%;
                padding: 20px; /* 调整内边距 */
            }
             .modal-content {
                right: 10px;
                top: 50%;
                transform: translate(-50%, -50%);
                max-width: calc(100% - 20px);
                 padding: 25px;
             }
             .drag-hint {
                right: 10px;
             }
             body {
                padding: 15px;
             }
        }
        .bookmarklet-card {
            opacity: 0;
            transform: translateY(15px); /* 调整动画 */
            transition: opacity 0.5s ease, transform 0.5s ease;
        }

        .bookmarklet-card.loaded {
            opacity: 1;
            transform: translateY(0);
        }
        /* 搜索框容器样式 */
        .search-container {
            margin: 10px auto 30px;
            max-width: 600px;
            padding: 0 10px; /* 调整内边距 */
            display: flex;
            align-items: center;
             z-index: 1;
        }

        /* 搜索输入框样式 */
        .search-input {
            width: 100%;
            padding: 12px 20px;
            border: 1px solid #d0d0d0; /* 边框颜色 */
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
            outline: none;
            background-color: #ffffff; /* 白色背景 */
            color: #333;
             box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .search-input:focus {
            border-color: #87ceeb; /* 浅蓝色边框 */
            box-shadow: 0 0 8px rgba(135, 206, 235, 0.3); /* 浅蓝色阴影 */
        }
         /* 搜索按钮样式 */
        .search-button {
            margin-left: 10px;
            min-width: 40px; /* 调整大小 */
            min-height: 40px;
            aspect-ratio: 1;
            background-color: linear-gradient(45deg, #ffb6c1 0%, #ff69b4 100%); /* 粉色渐变背景 */
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            flex-shrink: 0;
            box-shadow: 0 4px 10px rgba(255, 105, 180, 0.4); /* 粉色阴影 */
        }

        .search-button:hover {
            background-color: linear-gradient(45deg, #87ceeb 0%, #1e90ff 100%); /* 蓝色渐变背景 */
            transform: scale(1.05);
            box-shadow: 0 6px 15px rgba(30, 144, 255, 0.4); /* 蓝色阴影 */
             color: white;
        }

        .search-button svg {
            width: 20px; /* 调整图标大小 */
            height: 20px;
            transition: transform 0.2s ease;
             fill: white;
        }

        .search-button:hover svg {
            transform: scale(1.1);
        }

        /* 确保搜索容器垂直对齐 */
        .search-container {
             margin: 20px auto 30px;
        }
        .main-title {
            position: relative;
            display: inline-block;
            margin: 20px 0 0;
            font-size: 2.2em;
            color: #2c3e50;
             text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .badge.modern {
            position: absolute;
            top: 0;
            right: -15px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 20px;
            height: 20px;
            padding: 0 6px;
            font-size: 12px;
            font-weight: 500;
            color: white;
            background: linear-gradient(45deg, #ff8a65 0%, #ff5722 100%); /* 橙色渐变背景 */
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(255, 87, 34, 0.3); /* 橙色阴影 */
            transform-origin: center;
            animation: badgeAppear 0.3s ease-out;
        }

        @keyframes badgeAppear {
            from {
                transform: scale(0);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .badge.modern::before {
            content: none;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .main-title {
                font-size: 1.8em;
            }
             .badge.modern {
                right: -10px;
             }
        }
        /* 返回顶部按钮样式 */
        .back-to-top {
            bottom: 25px;
            opacity: 0;
            visibility: hidden;
            background-color: linear-gradient(45deg, #a1c4fd 0%, #c2e9fb 100%); /* 柔和蓝紫渐变背景 */
            z-index: 1000;
             box-shadow: 0 4px 10px rgba(161, 196, 253, 0.4); /* 阴影 */
             transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
        }
         .back-to-top.visible {
            opacity: 1;
            visibility: visible;
         }


        .help-icon:hover,
        .back-to-top:hover,
        .email-icon:hover {
            transform: scale(1.1);
             box-shadow: 0 6px 15px rgba(0,0,0,0.1);
        }
         .help-icon:hover {
             box-shadow: 0 6px 15px rgba(30, 144, 255, 0.5);
         }
          .email-icon:hover {
             box-shadow: 0 6px 15px rgba(255, 105, 180, 0.6);
          }
           .back-to-top:hover {
             box-shadow: 0 6px 15px rgba(161, 196, 253, 0.6);
          }


        .back-to-top svg {
            width: 20px;
            height: 20px;
            fill: #555; /* 深灰色填充 */
        }
        /* 添加到现有的 CSS 样式中 */
        .bookmarklet-card {
            /* 保持现有样式 */
            position: relative;
            overflow: hidden; /* 确保背景图不会溢出卡片 */
        }

        .bookmarklet-card::after {
            content: '';
            position: absolute;
            bottom: 15px;
            right: 15px;
            width: 24px;
            height: 24px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.3; /* 调整透明度 */
            transition: opacity 0.3s ease;
            pointer-events: none;
             filter: none;
        }

        .bookmarklet-card:hover::after {
            opacity: 0.5;
             filter: none;
        }
        /* 邮件图标和提示的容器 */
        .help-container,
        .email-container {
            position: fixed;
            top: 80px;
            right: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
        }
        .help-container{
            top: 25px;
        }

        /* 邮件提示文字样式 */
        .help-tooltip,
        .email-tooltip {
            background: rgba(0,0,0,0.6); /* 半透明深色背景 */
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            position: absolute;
            right: 45px;
            top: 50%;
            transform: translateY(-50%) translateX(10px);
            white-space: nowrap;
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: none;
             border: none;
             box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* 提示框的小三角 */
        .help-tooltip::after,
        .email-tooltip::after {
            content: '';
            position: absolute;
            right: -6px;
            top: 50%;
            transform: translateY(-50%);
            border-width: 6px 0 6px 6px;
            border-style: solid;
            border-color: transparent transparent transparent rgba(0,0,0,0.6); /* 三角颜色与背景一致 */
        }

        /* 鼠标悬浮时显示提示 */
        .help-container:hover .help-tooltip,
        .email-container:hover .email-tooltip {
            opacity: 1;
            transform: translateY(-50%) translateX(0);
        }
        .fixedNav{
            position:fixed;
            top:0px;
            left:0px;
            width:100%;
            z-index:999;
            background: linear-gradient(135deg, rgba(161, 196, 253, 0.95) 0%, rgba(194, 233, 251, 0.95) 100%); /* 柔和蓝紫渐变背景 */
            padding: 12px 0;
             box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        /* FAQ Section Styles */
        .faq-section {
            max-width: 800px;
            margin: 60px auto 40px;
            padding: 40px;
            transition: all 0.3s ease;
             background: linear-gradient(135deg, #e0f2f7 0%, #b2ebf2 100%); /* 柔和蓝绿渐变背景 */
             border-radius: 12px;
             border: none;
             box-shadow: 0 6px 20px rgba(0, 188, 212, 0.3);
             color: #333;
             position: relative;
             z-index: 1;
        }

        .faq-section h2 {
            margin: 0 auto;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: 600;
            position: relative;
            display: block;
            text-align: center;
            color: #00796b; /* 深青色标题 */
             text-shadow: none;
        }

        .faq-section p {
            color: #555;
            line-height: 1.7;
            margin-bottom: 15px;
            font-size: 0.95em;
        }

        .faq-section ul {
            list-style: none;
            padding-left: 0;
        }

        .faq-section li {
            margin-bottom: 10px;
            padding-left: 25px;
            position: relative;
            line-height: 1.6;
            color: #333;
        }

        .faq-section li::before {
            content: "•";
            color: #00acc1; /* 浅青色圆点 */
            font-size: 1.3em; /* 调整字体大小 */
            position: absolute;
            left: 5px;
            top: 2px;
             filter: none;
        }

        .faq-section strong {
            color: #00796b; /* 深青色加粗文字 */
            font-weight: 600;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .faq-section {
                margin: 30px 15px;
                padding: 25px;
            }
             .faq-section li {
                padding-left: 20px;
             }
        }
        /* 添加错误消息样式 */
        .error-message {
            padding: 15px;
            background: #fff9c4; /* 浅黄色背景 */
            border: 1px solid #fff59d; /* 浅黄色边框 */
            color: #fbc02d; /* 深黄色文字 */
            border-radius: 4px;
            text-align: center;
            margin: 20px auto;
             max-width: 600px;
             box-shadow: 0 1px 5px rgba(0,0,0,0.05);
        }
</style>
</head>
<body>
    <h1 class="main-title">
        📖 小书签驿站 
        <span class="badge modern" id="totalCount" title="当前收集小书签数量">0</span>
    </h1>
    <div class="zsline">
        <svg aria-hidden="true" viewBox="0 0 418 42" class="absolute left-0 top-full -mt-1 h-[0.4em] w-full" preserveAspectRatio="none"><path d="M0 21c118-10.5 260.5-12 418 0" stroke="#4285F4" stroke-width="6" fill="none" stroke-linecap="round"></path><defs><linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%"><stop offset="0%" stop-color="var(--theme-color1)"></stop><stop offset="100%" stop-color="var(--theme-color2)"></stop></linearGradient></defs></svg>
    </div>
    <h4>Your Bookmarklet Station</h4>

    <div class="help-container">
        <div class="help-tooltip">使用说明</div>
        <div class="help-icon" onclick="showModal()">?</div>
    </div>
    
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <span class="modal-close" onclick="hideModal()">×</span>
            <h2>💡 使用说明</h2>
            <p>1. 将感兴趣的功能标题拖动到浏览器的书签栏</p>
            <p>2. 在需要使用功能时，点击书签栏中新增加的书签即可</p>
        </div>
    </div>
    <div class="email-container">
        <div class="email-tooltip">Email：<EMAIL></div>
        <a href="mailto:<EMAIL>" class="email-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2zm13 2.383-4.708 2.825L15 11.105V5.383zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741zM1 11.105l4.708-2.897L1 5.383v5.722z"/>
            </svg>
        </a>
    </div>
    
    <div class="tags-container" id="tagsContainer">
        <span class="tag active" data-tag="all">
            <i><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-list"><path d="M3 12h.01"></path><path d="M3 18h.01"></path><path d="M3 6h.01"></path><path d="M8 12h13"></path><path d="M8 18h13"></path><path d="M8 6h13"></path></svg></i>
            全部</span>
    </div>

    <!-- 添加搜索框 -->
    <div class="search-container">
        <input type="text" id="searchInput" placeholder="搜索标题或描述..." class="search-input">
        <button id="searchButton" class="search-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
        </button>
    </div>

    <div class="bookmarklets-container" id="bookmarkletsContainer"></div>

    <div class="bookmark-bar-hint" id="bookmarkBarHint">
        将<span>标题</span>拖到浏览器的书签栏
    </div>

    <script>
        let currentDemoTimeout;
        let currentHighlightedCard;

        function showDragDemo() {
            // 清除之前的演示
            if (currentDemoTimeout) {
                clearTimeout(currentDemoTimeout);
            }
            if (currentHighlightedCard) {
                const oldHint = currentHighlightedCard.querySelector('.drag-hint');
                if (oldHint) oldHint.remove();
            }

            // 优先选择 active-card 进行演示
            let targetCard = document.querySelector('.bookmarklet-card.active-card');

            // 如果没有 active-card，则选择第一个书签卡片进行演示
            if (!targetCard) {
                targetCard = document.querySelector('.bookmarklet-card');
            }

            // 如果没有卡片，则直接返回
            if (!targetCard) return;

            currentHighlightedCard = targetCard;

            // 创建拖动提示元素
            const dragHint = document.createElement('div');
            dragHint.className = 'drag-hint';
            dragHint.style.position = 'absolute';
            dragHint.style.left = '5px'; // 修改为 right
            dragHint.style.top = '40px';
            dragHint.style.transform = 'translateY(0%)';

            // 将提示添加到卡片中
            targetCard.appendChild(dragHint);

            // 启动动画
            setTimeout(() => dragHint.classList.add('show'), 100);
        }

        // 显示/隐藏帮助模态框
        function showModal() {
            document.getElementById('helpModal').classList.add('show');
            document.getElementById('bookmarkBarHint').classList.add('show');

            // 移除其他卡片的 active-card 类
            document.querySelectorAll('.bookmarklet-card').forEach(card => {
                card.classList.remove('active-card');
            });

            // 为第一个卡片添加 active-card 类
            const firstCard = document.querySelector('.bookmarklet-card');
            if (firstCard) {
                firstCard.classList.add('active-card');
            }

            // 延迟执行 showDragDemo，确保 active-card 已经生成
            setTimeout(showDragDemo, 100);
        }

        function hideModal() {
            document.getElementById('helpModal').classList.remove('show');
            document.getElementById('bookmarkBarHint').classList.remove('show');
            // 清除演示效果
            if (currentDemoTimeout) {
                clearTimeout(currentDemoTimeout);
            }
            if (currentHighlightedCard) {
                const hint = currentHighlightedCard.querySelector('.drag-hint');
                if (hint) hint.remove();
            }

            // 移除所有卡片的 active-card 类
            document.querySelectorAll('.bookmarklet-card').forEach(card => {
                card.classList.remove('active-card');
            });
        }

        // 点击模态框外部关闭
        document.getElementById('helpModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });

        let bookmarklets = [];

        // 加载 bookmarklets 数据
        async function loadBookmarklets() {
            try {
                const response = await fetch('data/bookmarklets.json');
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                const data = await response.json();
                bookmarklets = data.bookmarklets;
                
                // 更新总数角标
                const totalCount = document.getElementById('totalCount');
                totalCount.textContent = bookmarklets.length;
                
                // 初始化标签和书签
                initializeTags();
                renderBookmarklets();
                
                // 初始化懒加载观察器
                initLazyLoad();
            } catch (error) {
                console.error('Error loading bookmarklets:', error);
                const container = document.getElementById('bookmarkletsContainer');
                container.innerHTML = '<div class="error-message">加载书签数据失败，请刷新页面重试</div>';
            }
        }

        // 添加新的懒加载初始化函数
        function initLazyLoad() {
            const options = {
                rootMargin: '0px',
                threshold: 0.1
            };

            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const card = entry.target;
                        card.classList.add('loaded');
                        observer.unobserve(card);
                    }
                });
            }, options);

            // 观察所有卡片
            document.querySelectorAll('.bookmarklet-card').forEach(card => {
                observer.observe(card);
            });
        }


        // 初始化标签
        function initializeTags() {
            const allTags = [...new Set(bookmarklets.flatMap(b => b.tags))];
            const tagsContainer = document.getElementById('tagsContainer');
            
            // 保留"全部"标签
            const allTagElement = tagsContainer.querySelector('[data-tag="all"]');
            tagsContainer.innerHTML = '';
            tagsContainer.appendChild(allTagElement);
            
            // 为"全部"标签添加点击事件
            allTagElement.addEventListener('click', () => {
                document.querySelectorAll('.tag').forEach(t => t.classList.remove('active'));
                allTagElement.classList.add('active');
                renderBookmarklets('all');
            });
            
            // 添加其他标签
            allTags.forEach(tag => {
                const tagElement = document.createElement('span');
                tagElement.className = 'tag';
                tagElement.textContent = tag;
                tagElement.dataset.tag = tag;
                tagElement.addEventListener('click', () => {
                    document.querySelectorAll('.tag').forEach(t => t.classList.remove('active'));
                    tagElement.classList.add('active');
                    renderBookmarklets(tag);
                });
                tagsContainer.appendChild(tagElement);
            });
        }

        // 渲染书签列表（保持原有的渲染函数不变）
        function renderBookmarklets(filterTag = 'all', customBookmarklets = null) {
                const container = document.getElementById('bookmarkletsContainer');
                const containerWidth = container.offsetWidth;
                container.innerHTML = '';

                const bookmarkletsToRender = customBookmarklets || bookmarklets;
                const filteredBookmarklets = filterTag === 'all' 
                    ? bookmarkletsToRender 
                    : bookmarkletsToRender.filter(b => b.tags.includes(filterTag));

                filteredBookmarklets.forEach(bookmarklet => {
                const card = document.createElement('div');
                card.className = 'bookmarklet-card';

                // 如果有sourceIcon，添加背景图
                if (bookmarklet.sourceIcon) {
                    card.style.setProperty('--source-icon', `url('${bookmarklet.sourceIcon}')`);
                    // 使用CSS自定义属性设置背景图
                    const style = document.createElement('style');
                    style.textContent = `
                        .bookmarklet-card[style*='--source-icon']::after {
                            background-image: var(--source-icon);
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                // 创建收藏图标
                const bookmarkIcon = document.createElement('div');
                bookmarkIcon.className = 'bookmark-icon';
                bookmarkIcon.title = '添加到收藏夹';
                
                // 添加点击事件
                bookmarkIcon.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    const isBookmarked = bookmarkIcon.classList.contains('active');

                    if (isBookmarked) {
                        // 如果已经收藏，则取消收藏
                        bookmarkIcon.classList.remove('active');
                        // 这里可以添加取消收藏的逻辑，例如从本地存储中移除
                    } else {
                        // 如果未收藏，则显示帮助模态框
                        showModal();

                        // 移除其他卡片的 active-card 类
                        document.querySelectorAll('.bookmarklet-card').forEach(card => {
                            card.classList.remove('active-card');
                        });

                        // 为当前卡片添加 active-card 类
                        card.classList.add('active-card');

                        // 添加active类来改变图标样式
                        bookmarkIcon.classList.add('active');
                    }
                });
                
                // 构建卡片内容
                let cardHTML = `
                    <a href="${bookmarklet.code.replace(/"/g, '&quot;')}" 
                       class="bookmarklet-title" 
                       title="拖拽链接到书签栏即可完成安装" 
                       ondragstart="this.style.opacity='0.4'"
                       ondragend="this.style.opacity='1'"
                       onclick="event.preventDefault();">${bookmarklet.title}<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="external-svg"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></a>
                    <div class="bookmarklet-description">${bookmarklet.description}</div>
                    <div class="bookmarklet-tags">
                        ${bookmarklet.tags.map(tag => `<span class="bookmarklet-tag">${tag}</span>`).join('')}
                    </div>
                `;
                
                // 如果有来源信息，添加来源链接
                if (bookmarklet.source && bookmarklet.sourceUrl) {
                    cardHTML += `
                    <a href="${bookmarklet.sourceUrl}" class="bookmarklet-source" target="_blank" title="查看来源">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                        ${bookmarklet.source}
                    </a>`;
                }
                
                card.innerHTML = cardHTML;
                card.appendChild(bookmarkIcon);
                container.appendChild(card);
            });

            // 强制重新计算布局
            container.style.width = containerWidth + 'px';
            requestAnimationFrame(() => {
                container.style.width = '100%';
            });

            // 重新初始化懒加载观察器
            initLazyLoad();
        }

        // 添加错误消息样式
        const style = document.createElement('style');
        style.textContent = `
            .error-message {
                padding: 20px;
                background: #fff3cd;
                border: 1px solid #ffeeba;
                color: #856404;
                border-radius: 8px;
                text-align: center;
                margin: 20px 0;
            }
        `;
        document.head.appendChild(style);

        // 在 loadBookmarklets 函数后添加
        function initializeSearch() {
            const searchInput = document.getElementById('searchInput');
            const searchButton = document.getElementById('searchButton');

            function performSearch() {
                const searchTerm = searchInput.value.toLowerCase().trim();
                const activeTag = document.querySelector('.tag.active')?.dataset.tag || 'all';
                
                // 过滤符合条件的书签
                const filteredBookmarklets = bookmarklets.filter(bookmarklet => {
                    const matchesTag = activeTag === 'all' || bookmarklet.tags.includes(activeTag);
                    const matchesSearch = 
                        bookmarklet.title.toLowerCase().includes(searchTerm) ||
                        bookmarklet.description.toLowerCase().includes(searchTerm);
                    return matchesTag && matchesSearch;
                });

                // 渲染搜索结果
                const container = document.getElementById('bookmarkletsContainer');
                container.innerHTML = '';

                if (filteredBookmarklets.length === 0) {
                    container.innerHTML = '<div class="error-message">没有找到匹配的结果</div>';
                    return;
                }

                // 渲染过滤后的结果
                renderBookmarklets(activeTag, filteredBookmarklets);
            }

            // 点击搜索按钮时执行搜索
            searchButton.addEventListener('click', performSearch);

            // 按回车键时也执行搜索
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }

        // 在 document.addEventListener('DOMContentLoaded', ...) 中添加
        document.addEventListener('DOMContentLoaded', () => {
            loadBookmarklets();
            initializeSearch();
        });

        //窗口向下滚动后，自动为tags-container增加class：.fixedNav
        window.addEventListener('scroll', () => {
            const tagsContainer = document.getElementById('tagsContainer');
            if (window.scrollY > 120) {
            tagsContainer.classList.add('fixedNav');
            } else {
            tagsContainer.classList.remove('fixedNav');
            }
        });
    </script>

    <div class="faq-section">
        <h2>Bookmarklet简介</h2>
        <p>Bookmarklet是一段JavaScript脚本代码，它通过浏览器书签的形式保存，本质上是一个可执行的「特殊链接」。与传统书签仅保存网页地址不同，Bookmarklet 的特点在于：</p>
        <ul>
            <li><strong>即装即用：</strong>只需将代码链接拖拽到浏览器的书签栏，即可完成安装。</li>
            <li><strong>一键触发：</strong>点击书签时，脚本会在当前页面自动执行，实现快速功能操作（如修改页面样式、提取内容等）。</li>
            <li><strong>轻量化工具：</strong>无需安装扩展插件，不占用系统资源，却能像小工具一样灵活解决网页浏览中的高频需求。</li>
        </ul>
    </div>
    <footer class="footer">
        <p>© 2025 <a href="http://bookmarklet.scxp.com">bookmarklet.scxp.com</a> All Rights Reserved.</p>
    </footer>
     <!-- 添加返回顶部按钮 -->
     <div class="back-to-top">
        <svg viewBox="0 0 24 24">
            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
        </svg>
    </div>
    <script>
        // 返回顶部功能
        const backToTop = document.querySelector('.back-to-top');
        
        // 监听滚动事件
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });

        // 点击返回顶部
        backToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>









