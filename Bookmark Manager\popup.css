/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    max-width: 1280px;
    min-width: 300px;
    margin: 0 auto;
    padding: 15px;
    line-height: 1.6;
    box-sizing: border-box;
}
a {
    color: #333;
    text-decoration: none;
}
a:hover {
    color: #22C55E;
}
.container {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 8px 8px -3px rgba(0, 0, 0, 0.1); /* 只在底部显示阴影 */
}

h1 {
    font-size: 22px;
    color: #333; /* 更新：更深的标题颜色 */
    text-align: center;
    margin-top: 0;
    margin-bottom: 10px;
    font-weight: 600;
    display: flex; 
    align-items: center;
    justify-content: center;
}
.container h1 svg{
    width: 20px;
    height: 20px;
    margin-right: 5px;
    vertical-align: middle;
    color: #22C55E;
}
/* 控制区域 */
.controls {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 12px;
}
#searchInput,
#sortOrder {
    padding: 10px 12px;
    border: 1px solid #D1D5DB; /* 更新：浅灰色边框 */
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    padding-right: 28px;
    background-color: #FFFFFF; /* 更新：白色背景 */
    color: #374151; /* 更新：输入框文本颜色 */
}
#searchInput {
    box-sizing:border-box; 
    background-color: #E9EDF1;
    text-indent: 20px;
    position: relative; /* 添加相对定位 */
}
#searchInput::placeholder{
    color: #374151;
}
.search-wrapper::before {
    content: '🔍';
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    color: #666;
    z-index: 1;
    pointer-events: none;
}
#sortOrder {
    flex-grow: 0;
    background-color: #E9EDF1;
}

#searchInput:focus,
#sortOrder:focus {
    border-color: #666; 
    box-shadow: 0 4px 12px rgba(4, 48, 33, 0.1);
    outline: none;
}

#clearSearchInput {
    display: none;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: transparent;
    padding: 0;
    cursor: pointer;
    color: #888;
    font-size: 1.1em;
}
.search-wrapper{
    position: relative; 
    flex-grow: 1;
}

/* 重置所有按钮 */
#resetAllButton {
    background-color: #6B7280; /* 更新：中性灰色 */
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease, transform 0.3s ease;
    flex-shrink: 0;
}

#resetAllButton:hover {
    background-color: #f00; /* 更新：深灰色悬停 */
    transform: rotate(180deg);
}
#resetAllButton i{
    font-size: 20px;
}

/* 书签列表 */
#bookmarksList {
    max-height: 320px;
    overflow-y: auto;
    padding-right: 5px;
    overflow-x: hidden;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

/* 书签项 */
.bookmark-item {
    background-color: #fff;
    border: 1px solid #E5E7EB; /* 更新：更浅的边框 */
    padding: 15px;
    padding-bottom: 25px;
    border-radius: 8px;
    position: relative;
    transition: box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out; /* 新增：边框颜色过渡 */
    min-width: 0;
}

.bookmark-item:hover {
    border-color: #666; /* 更新：灰色悬停边框 */
    box-shadow: 0 4px 12px rgba(4, 48, 33, 0.1); /* 更新：主题黑色悬停阴影 */
}

.bookmark-item h3 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #1F2937; /* 更新：深色标题 */
    font-weight: 500;
    word-break: break-all;
    white-space: nowrap;/*设置不折行*/
    overflow: hidden;/*超出部分自动隐藏*/
    text-overflow: ellipsis;/*省略号*/
}

.bookmark-item h3 .click-count {
    background: linear-gradient(45deg, #333333, #000000);
    color: white;
    padding: 2px 5px;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1); /* 更新：更柔和的阴影 */
}

.bookmark-item p {
    margin: 4px 0 8px 0;
    font-size: 13px;
    color: #6B7280; /* 更新：中性灰色文本 */
    line-height: 1.5;
}

/* 操作按钮容器 */
.actions {
    position: absolute;
    bottom: 12px;
    right: 12px;
    display: flex;
    gap: 8px;
}

/* 通用操作按钮样式 */
.actions button {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.2s ease;
    color: white;
}

.actions button:hover {
    transform: scale(1.1);
}

/* 编辑/重置单个书签按钮 */
.reset-single {
    background-color: #10B981; /* 更新：现代绿色/青色 */
}

.reset-single:hover {
    background-color: #059669; /* 更新：深一点的绿色/青色 */
}

/* 删除书签按钮 */
.delete-bookmark {
    background-color: #EF4444; /* 更新：现代红色 */
}

.delete-bookmark:hover {
    background-color: #DC2626; /* 更新：深一点的红色 */
}

/* 图标样式 (假设您在HTML中使用了 <i> 标签或其他方式嵌入图标) */
.actions button i {
    font-size: 12px;/* 图标大小 */
    line-height: 1;
    transition: color 0.2s ease;
    /* 添加颜色过渡效果 */
}

/* 图标悬停效果 */
.actions button:hover i {
    color: #f8f9fa;
    /* 悬停时图标颜色变亮 */
}

/* 滚动条美化 (可选, Webkit内核浏览器) */
#bookmarksList::-webkit-scrollbar {
    width: 6px;
}

#bookmarksList::-webkit-scrollbar-track {
    background: #F3F4F6; /* 更新：浅灰色轨道 */
    border-radius: 3px;
}

#bookmarksList::-webkit-scrollbar-thumb {
    background: #9CA3AF; /* 更新：中性灰色滑块 */
    border-radius: 3px;
}

#bookmarksList::-webkit-scrollbar-thumb:hover {
    background: #6B7280; /* 更新：深灰色滑块悬停 */
}

.footer {
    position: relative;
    margin-top: 20px; /* 新增：在书签列表和页脚之间添加间距 */
}
.option-group{
    position: absolute;
    right: 0px; /* 修改：对齐到页脚内容区的右边缘 */
    top: 50%;   /* 修改：为垂直居中做准备 */
    transform: translateY(-50%); /* 新增：实现垂直居中 */
    text-align: right;
}
/* 设置部分样式 */
.settings-section {
    margin: 10px 0;
    padding: 10px;
    background-color: #F3F4F6; /* 更新：浅灰色背景 */
    border-radius: 6px;
}

.setting-option {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #374151; /* 更新：深灰色文本 */
    cursor: pointer;
}

.setting-option input[type="checkbox"] {
    margin-right: 8px;
}

/* 响应式，屏幕分辨率大于1280px时，将滚动条的宽度设置为0 */
@media screen and (min-width: 1280px) {
    #bookmarksList {
        max-height: 670px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px; /* 修改：增大网格项之间的间距 */
    }
    .bookmark-item h3 {
        white-space: normal;
        overflow: visible;
}
    #searchInput {
        width: 100%;
    }
    .bookmark-details {
        display: block !important;
    }

    .footer a {
        display: none;
    }
}