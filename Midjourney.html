<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Midjourney图片切割</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    }
    .container {
      text-align: center;
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.06);
      max-width: 800px;
      width: 100%;
      box-sizing: border-box;
      transition: border 0.2s ease, box-shadow 0.2s ease; /* 添加过渡效果 */
    }
    /* 新增：拖拽悬停时的样式 */
    .container.dragover {
        border: 2px dashed #007bff;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.06), inset 0 0 20px rgba(0, 123, 255, 0.1);
    }
    header {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      margin-bottom: 20px;
    }
    h1 {
      margin: 0;
      font-size: 24px;
      text-align: center;
      width: 100%;
    }
    h5{
        font-size: 12px;
        color: #888;
    }
    .skin-button {
      position: absolute;
      right: 0;
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: #007bff;
    }
    .controls {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 20px;
    }
    .control-group {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    label {
      font-size: 14px;
      margin-bottom: 5px;
    }
    input[type="number"] {
      padding: 5px;
      font-size: 14px;
      border: 1px solid #ccc;
      border-radius: 5px;
      text-align: center;
      width: 60px;
    }
    #preview {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      margin-top: 20px;
    }
    .image-item {
      border: 1px solid #ccc;
      padding: 5px;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    canvas {
      width: 100%;
      height: auto;
      display: block;
    }
    button {
      padding: 10px 20px;
      font-size: 16px;
      cursor: pointer;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      transition: background-color 0.3s ease;
    }
    #downloadButton{
        margin-top: 20px;
    }
    #downloadButton:hover {
      background-color: #0056b3;
    }
    input[type="file"] {
      padding: 10px;
      font-size: 16px;
      border: 1px solid #ccc;
      border-radius: 5px;
      background-color: #fff;
      cursor: pointer;
    }
    @media (max-width: 768px) {
      #preview {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    /* Grid Selection Styles */
    #gridSelectionContainer {
      position: absolute;
      background-color: white;
      border: 1px solid #ccc;
      padding: 10px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 8px;
      display: none;
    }

    #gridSelection {
      display: grid;
      grid-template-columns: repeat(10, 25px);
      grid-template-rows: repeat(10, 25px);
      border: 1px solid #eee;
      width: 250px; /* 10 * 25px */
      height: 250px; /* 10 * 25px */
    }

    .grid-cell {
      width: 25px;
      height: 25px;
      border: 1px solid #eee;
      box-sizing: border-box;
      margin: 4px;
      padding: 8px;
      border: 2px solid #dcdcdc;
      transition: all 0.2s ease;
    }
    .grid-cell:hover {
      border-color: #409eff;
      transform: scale(1.05);
    }
    .grid-cell.hovered {
      background-color: #cceeff;
    }

    .grid-cell.selected {
      background-color: #007bff;
    }

    #gridSizeDisplay {
      text-align: center;
      margin-top: 10px;
      font-size: 14px;
      color: #555;
      font-weight: bold;
    }
    .gallery-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 15px;
    }
    #showGridSelection{
      background: none;
      font-size: 30px;
      padding-left: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>Midjourney图片切割</h1>
      <button id="skinButton" class="skin-button">🎨</button>
    </header>
    <div class="controls">
      <div class="control-group">
        <label for="rows">行数:</label>
        <input type="number" id="rows" min="1" value="2" max="10">
      </div>
      <div class="control-group">
        <label for="columns">列数:</label>
        <input type="number" id="columns" min="1" value="2" max="10">
      </div>
      <div class="control-group">
        <button id="showGridSelection">🧲</button>
      </div>
    </div>
    <div id="gridSelectionContainer">
      <div id="gridSelection"></div>
      <div id="gridSizeDisplay"></div>
    </div>
    <h5>支持上传多张图片,切割图片下载后，利用File System Access API删除原图（拖拽上传不支持此功能）</h5>
    <button id="selectFiles">选择图片 (或将图片拖拽至此)</button>
    <div id="preview"></div>
    <button id="downloadButton" disabled>下载所有图片</button>
  </div>

  <script>
    document.getElementById('selectFiles').addEventListener('click', handleFileSelect);
    document.getElementById('downloadButton').addEventListener('click', downloadAllImages);
    document.getElementById('skinButton').addEventListener('click', toggleSkin);
    document.getElementById('rows').addEventListener('change', updateGridSettings);
    document.getElementById('columns').addEventListener('change', updateGridSettings);
    document.getElementById('showGridSelection').addEventListener('click', showGridSelection);

    let imageParts = [];
    let isRowLayout = true;
    let fileHandles = [];
    let currentFiles = []; // 新增：统一管理已加载的文件对象
    let processedImages = 0;
    let totalImages = 0;
    let currentRows = 2;
    let currentColumns = 2;

    const gridSelectionContainer = document.getElementById('gridSelectionContainer');
    const gridSelection = document.getElementById('gridSelection');
    const gridSizeDisplay = document.getElementById('gridSizeDisplay');
    const rowsInput = document.getElementById('rows');
    const columnsInput = document.getElementById('columns');

    // Populate grid selection
    for (let i = 0; i < 100; i++) {
      const cell = document.createElement('div');
      cell.classList.add('grid-cell');
      cell.dataset.row = Math.floor(i / 10) + 1;
      cell.dataset.col = (i % 10) + 1;
      gridSelection.appendChild(cell);
    }

    gridSelection.addEventListener('mouseover', (e) => {
      const hoveredCell = e.target.closest('.grid-cell');
      if (hoveredCell) {
        const row = parseInt(hoveredCell.dataset.row);
        const col = parseInt(hoveredCell.dataset.col);
        highlightGrid(row, col);
        gridSizeDisplay.textContent = `${row} 行 x ${col} 列`;
      }
    });

    gridSelection.addEventListener('click', (e) => {
      const clickedCell = e.target.closest('.grid-cell');
      if (clickedCell) {
        const row = parseInt(clickedCell.dataset.row);
        const col = parseInt(clickedCell.dataset.col);
        rowsInput.value = row;
        columnsInput.value = col;
        updateGridSettings();
        gridSelectionContainer.style.display = 'none';
      }
    });

    function highlightGrid(rows, cols) {
      const cells = gridSelection.querySelectorAll('.grid-cell');
      cells.forEach(cell => {
        const cellRow = parseInt(cell.dataset.row);
        const cellCol = parseInt(cell.dataset.col);
        if (cellRow <= rows && cellCol <= cols) {
          cell.classList.add('hovered');
        } else {
          cell.classList.remove('hovered');
        }
      });
    }

    function showGridSelection() {
      gridSelectionContainer.style.display = 'block';
      const buttonRect = document.getElementById('showGridSelection').getBoundingClientRect();
      gridSelectionContainer.style.top = `${buttonRect.bottom + window.scrollY + 10}px`;
      gridSelectionContainer.style.left = `${buttonRect.left + window.scrollX}px`;
    }

    document.addEventListener('click', (e) => {
      if (!gridSelectionContainer.contains(e.target) && e.target.id !== 'showGridSelection') {
        gridSelectionContainer.style.display = 'none';
      }
    });

    // 修改：重写文件选择函数
    async function handleFileSelect() {
      try {
        fileHandles = await window.showOpenFilePicker({
          multiple: true,
          types: [{
            description: '图片文件',
            accept: {
              'image/*': ['.png', '.jpg', '.jpeg', '.webp']
            }
          }]
        });

        currentFiles = await Promise.all(fileHandles.map(handle => handle.getFile()));
        processImagesFromState();

      } catch (err) {
        console.error('Error accessing files:', err);
      }
    }
    
    // 新增：一个集中的函数来处理所有加载的图片
    function processImagesFromState() {
      const previewContainer = document.getElementById('preview');
      previewContainer.innerHTML = '';
      imageParts = [];
      processedImages = 0;
      totalImages = currentFiles.length;
      document.getElementById('downloadButton').disabled = true;

      if (totalImages === 0) return;

      currentFiles.forEach(file => {
        if (!file.type.startsWith('image/')) {
            console.warn('Skipping non-image file:', file.name);
            totalImages--;
            return;
        }
        const img = new Image();
        img.src = URL.createObjectURL(file);
        img.onload = () => {
          processImage(img, file.name);
          processedImages++;
          if (processedImages === totalImages && totalImages > 0) {
            document.getElementById('downloadButton').disabled = false;
          }
        };
        img.onerror = () => {
            console.error('Could not load image:', file.name);
            totalImages--;
        };
      });
    }

    // 修改：重写网格更新函数
    function updateGridSettings() {
      currentRows = parseInt(document.getElementById('rows').value);
      currentColumns = parseInt(document.getElementById('columns').value);
      
      if (currentFiles.length > 0) {
        processImagesFromState();
      }
    }

    function processImage(img, originalFileName) {
      const width = img.width;
      const height = img.height;
      const cellWidth = Math.floor(width / currentColumns);
      const cellHeight = Math.floor(height / currentRows);

      const baseName = originalFileName.replace(/\.[^/.]+$/, '');
      const totalCells = currentRows * currentColumns;

      for (let i = 0; i < totalCells; i++) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = cellWidth;
        canvas.height = cellHeight;
        const column = i % currentColumns;
        const row = Math.floor(i / currentColumns);
        const sx = column * cellWidth;
        const sy = row * cellHeight;
        ctx.drawImage(img, sx, sy, cellWidth, cellHeight, 0, 0, cellWidth, cellHeight);
        const container = document.createElement('div');
        container.className = 'image-item';
        container.appendChild(canvas);
        document.getElementById('preview').appendChild(container);
        imageParts.push({ 
          canvas, 
          fileName: `${baseName}-part-${i + 1}.png`
        });
      }
      updateLayout();
    }

    async function downloadAllImages() {
      const downloadButton = document.getElementById('downloadButton');
      downloadButton.disabled = true;
      downloadButton.textContent = '下载中...';

      try {
        const downloadPromises = imageParts.map(({ canvas, fileName }) => {
          return new Promise((resolve) => {
            canvas.toBlob(async (blob) => {
              const link = document.createElement('a');
              link.href = URL.createObjectURL(blob);
              link.download = fileName;
              link.click();
              setTimeout(() => {
                URL.revokeObjectURL(link.href);
                resolve();
              }, 100);
            }, 'image/png');
          });
        });
        await Promise.all(downloadPromises);

        // 仅在通过文件选择器选择文件时（fileHandles有内容），才尝试删除
        if (fileHandles.length > 0) {
            const deletePromises = fileHandles.map(handle => handle.remove());
            await Promise.all(deletePromises);
        }

        imageParts = [];
        fileHandles = [];
        currentFiles = [];
        processedImages = 0;
        totalImages = 0;

        const previewContainer = document.getElementById('preview');
        previewContainer.innerHTML = '';
        
        downloadButton.disabled = true;
        downloadButton.textContent = '下载所有图片';

      } catch (err) {
        console.error('Error during download or deletion:', err);
        downloadButton.textContent = '下载失败，请重试';
      }
    }

    function toggleSkin() {
      isRowLayout = !isRowLayout;
      updateLayout();
      const skinButton = document.getElementById('skinButton');
      skinButton.textContent = isRowLayout ? '🎨' : '📑';
    }

    function updateLayout() {
      const previewContainer = document.getElementById('preview');
      if (isRowLayout) {
        previewContainer.style.gridTemplateColumns = `repeat(${currentColumns}, 1fr)`;
      } else {
        const totalProcessedParts = imageParts.length;
        previewContainer.style.gridTemplateColumns = `repeat(${totalProcessedParts}, 1fr)`;
      }
    }

    // 新增：拖放功能相关代码
    const dropZone = document.querySelector('.container');

    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('dragover');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('dragover');
        
        // 拖放的文件没有删除权限，所以清空 fileHandles
        fileHandles = []; 
        // 将拖放的 FileList 转换为数组并存入 currentFiles
        if (e.dataTransfer.files.length > 0) {
            currentFiles = Array.from(e.dataTransfer.files);
            processImagesFromState();
        }
    });

  </script>
</body>
</html>