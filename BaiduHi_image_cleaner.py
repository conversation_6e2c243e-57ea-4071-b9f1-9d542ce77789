# -*- coding: utf-8 -*-
r"""
如流聊天记录清理工具
目标目录：C:\Users\<USER>\Documents\Baidu\Baidu Hi\ce0140aaa1662bf4\My Images
功能：删除修改日期早于2024年1月1日的图片文件(jpg/png/gif/bmp)。
注意：操作涉及系统盘文件，可能需要管理员权限。
"""
import os
import time
import datetime
import shutil
from pathlib import Path
import ctypes
import sys

def is_admin():
    """检查脚本是否以管理员权限运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """尝试以管理员权限重新运行脚本"""
    if is_admin():
        return True
    else:
        # 重新以管理员身份运行
        ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)
        return False

def get_confirmation(message):
    """获取用户确认"""
    while True:
        response = input(message + " (y/n): ").lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        print("请输入 y 或 n")

def clean_old_images(target_dir, cutoff_date):
    """清理指定目录下早于截止日期的图片文件"""
    # 支持的图片格式
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
    
    # 统计信息
    total_files = 0
    deleted_files = 0
    error_files = 0
    
    print(f"开始扫描目录: {target_dir}")
    print(f"删除修改日期早于 {cutoff_date.strftime('%Y-%m-%d')} 的图片文件")
    
    # 遍历目录及子目录
    for root, dirs, files in os.walk(target_dir):
        for file in files:
            file_path = os.path.join(root, file)
            file_ext = os.path.splitext(file)[1].lower()
            
            # 只处理图片文件
            if file_ext in image_extensions:
                total_files += 1
                try:
                    # 获取文件修改时间
                    mod_time = os.path.getmtime(file_path)
                    mod_date = datetime.datetime.fromtimestamp(mod_time)
                    
                    # 如果文件修改日期早于截止日期，则删除
                    if mod_date < cutoff_date:
                        print(f"删除: {file_path} (修改日期: {mod_date.strftime('%Y-%m-%d')})")
                        os.remove(file_path)
                        deleted_files += 1
                except Exception as e:
                    print(f"处理文件时出错: {file_path} - {str(e)}")
                    error_files += 1
    
    # 输出统计信息
    print("\n清理完成!")
    print(f"扫描图片文件总数: {total_files}")
    print(f"删除的文件数: {deleted_files}")
    print(f"处理出错的文件数: {error_files}")
    # 防止窗口自动关闭
    input("清理程序执行完毕...")
def main():
    # 目标目录
    target_dir = r"C:\Users\<USER>\Documents\Baidu\Baidu Hi\ce0140aaa1662bf4\My Images"
    
    # 检查目录是否存在
    if not os.path.exists(target_dir):
        print(f"错误: 目标目录不存在: {target_dir}")
        return
    
    # 截止日期: 2024年1月1日
    cutoff_date = datetime.datetime(2024, 1, 1)
    
    # 获取用户确认
    print(f"将删除 {target_dir} 及其子目录中所有修改日期早于 {cutoff_date.strftime('%Y-%m-%d')} 的所有图片文件(jpg/png/gif/bmp)")
    if get_confirmation("确认要执行删除操作吗?"):
        clean_old_images(target_dir, cutoff_date)
    else:
        print("操作已取消")

if __name__ == "__main__":
    # 检查管理员权限
    if not is_admin():
        print("注意: 删除系统盘文件可能需要管理员权限")
        if get_confirmation("是否以管理员身份重新运行?"):
            run_as_admin()
        else:
            print("将尝试以当前权限运行，但可能无法删除某些文件")
            main()
    else:
        main()