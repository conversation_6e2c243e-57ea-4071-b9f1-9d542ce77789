<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>小红书封面生成器</title>
    <!-- 加载Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap" rel="stylesheet">
    <!-- 加载Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #E60012; /* 品牌红 */
            --accent-color: #FFD700; /* 强调金 */
            --bg-gradient: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        #cover-container {
            width: 600px;
            height: 800px; /* 3:4比例 */
            position: relative;
            overflow: hidden;
            font-family: 'Noto Sans SC', sans-serif;
        }

        .cover-canvas {
            width: 100%;
            height: 100%;
            background: var(--bg-gradient);
            padding: 40px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        /* 文字层次设计 */
        .title-main {
            font-size: 60px;
            font-weight: 900;
            line-height: 1.2;
            text-align: center;
            margin: 20px 0;
            position: relative;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .title-keyword {
            color: var(--primary-color);
            -webkit-text-stroke: 1px #fff; /* 文字描边 */
            position: relative;
            display: inline-block;
            padding: 0 8px;
        }

        .title-keyword::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--accent-color);
        }

        .subtitle {
            font-size: 20px;
            color: #666;
            margin: 15px 0;
            text-align: center;
            line-height: 1.5;
            max-width: 80%;
        }

        .decorative-line {
            width: 120px;
            height: 3px;
            background: var(--primary-color);
            margin: 25px 0;
        }

        /* 装饰元素 */
        .corner-decor {
            position: absolute;
            width: 80px;
            height: 80px;
            border: 3px solid var(--primary-color);
            opacity: 0.2;
        }
        .corner-decor.tl { top: -40px; left: -40px; }
        .corner-decor.br { bottom: -40px; right: -40px; }

        .download-btn {
            position: absolute;
            bottom: 30px;
            padding: 12px 30px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.3s;
        }
        .download-btn:hover {
            transform: translateY(-3px);
        }
    </style>
</head>
<body>
    <div id="cover-container">
        <div class="cover-canvas" id="cover">
            <div class="corner-decor tl"></div>
            <div class="corner-decor br"></div>
            
            <h1 class="title-main">
                <span class="title-keyword">百度营销</span>四川服务中心<br>
                <span style="font-size: 0.8em">西部领先的</span>
                <span class="title-keyword">互联网营销专家</span>
            </h1>
            
            <div class="decorative-line"></div>

            <p class="subtitle">
                专注搜索营销与信息流推广<br>
                服务全川千家企业的数字转型专家<br>
                大数据赋能精准营销解决方案
            </p>

            <div class="decorative-line"></div>
        </div>
        <button class="download-btn" onclick="generateImage()">
            <i class="fas fa-download"></i> 下载封面
        </button>
    </div>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        function generateImage() {
            // 隐藏下载按钮
            document.querySelector('.download-btn').style.display = 'none';
            
            html2canvas(document.querySelector("#cover"), {
                scale: 2, // 提高输出质量
                useCORS: true,
                backgroundColor: null
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = 'cover-design.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
                
                // 恢复按钮显示
                document.querySelector('.download-btn').style.display = 'block';
            });
        }
    </script>
</body>
</html>