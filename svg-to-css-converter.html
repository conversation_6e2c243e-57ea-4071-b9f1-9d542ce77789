<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG 转 CSS 背景转换器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f2f5; /* 更柔和的背景色 */
            color: #333;
            line-height: 1.6;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.2em;
            font-weight: 600;
        }
        .info-box {
            background-color: #e8f4f8;
            border-left: 5px solid #3498db;
            padding: 15px 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .info-box p {
            margin: 0;
            font-size: 0.95em;
            color: #555;
        }
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr; /* 两列布局 */
            gap: 30px;
        }
        .section {
            background-color: #ffffff;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            display: flex;
            flex-direction: column;
        }
        .section-full-width {
            grid-column: 1 / -1; /* 占据所有列 */
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #2c3e50;
            font-size: 1.1em;
        }
        textarea {
            width: 100%;
            min-height: 250px; /* 调整高度 */
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.95em;
            resize: vertical;
            background-color: #fdfdfd;
            box-sizing: border-box; /* 确保内边距和边框包含在宽度内 */
        }
        .tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        .tab {
            padding: 10px 20px;
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-bottom: none;
            border-radius: 6px 6px 0 0;
            cursor: pointer;
            margin-right: 5px;
            font-weight: 500;
            color: #495057;
            transition: background-color 0.2s, color 0.2s;
        }
        .tab:hover {
            background-color: #e2e6ea;
        }
        .tab.active {
            background-color: #ffffff;
            border-color: #dee2e6;
            border-bottom-color: #ffffff;
            color: #3498db;
            position: relative;
            z-index: 1;
        }
        .tab-content {
            display: none;
            flex-grow: 1; /* 让内容区域填充剩余空间 */
            flex-direction: column;
        }
        .tab-content.active {
            display: flex;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: background-color 0.3s, transform 0.1s;
            margin-top: 15px; /* 按钮上方间距 */
        }
        button:hover {
            background-color: #2980b9;
            transform: translateY(-1px);
        }
        button:active {
            transform: translateY(0);
        }
        .preview-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .preview-size-input {
            width: 70px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .preview-box {
            width: 100%;
            height: 250px; /* 调整高度 */
            border: 1px dashed #a0a0a0; /* 虚线边框 */
            border-radius: 6px;
            margin-top: 10px;
            background-color: #f8f8f8;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden; /* 确保背景图不会溢出 */
        }
        .options-group {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .options-group label {
            font-size: 1em;
            margin-bottom: 5px;
        }
        .options-group select, .options-group input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .svg_title{
            display: flex;
            align-items: center; /* 垂直方向的居中对齐 */
        }
        .svg_title a{
            height: 30px;
        }
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr; /* 小屏幕下变为单列布局 */
            }
        }
    </style>
</head>
<body>
    <h1>SVG 转 CSS 背景转换器</h1>
    
    <div class="info-box">
        <p>在 CSS 中使用 SVG 作为背景时，需要将 SVG 代码进行特殊处理。此工具可以帮助你将普通的 SVG 代码转换为可在 CSS 中使用的格式。</p>
    </div>

    <div class="main-grid">
        <div class="section section-full-width"> <!-- 输入部分占据整行 -->
            <label for="svgInput" class="svg_title">输入 SVG 代码：<a href="https://jakearchibald.github.io/svgomg/" target="_blank" title="svg压缩工具"><svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" class="icon" viewBox="0 0 1024 1024"><path fill="#1E50AE" d="M938.667 963.925H85.333a47.957 47.957 0 0 1-47.957-48.128V280.405a47.616 47.616 0 0 1 30.89-44.544L502.444 61.952a26.453 26.453 0 0 1 19.114 0l434.176 173.91a47.445 47.445 0 0 1 30.208 44.543v635.392a47.957 47.957 0 0 1-47.274 48.128zm-849.408-51.2h845.824V282.453L512 113.323 88.917 282.453zm-2.39-629.418zm-9.557-23.723z"/><path fill="#1E50AE" d="M512 716.8a25.6 25.6 0 0 1-25.6-25.6V467.115a25.6 25.6 0 0 1 51.2 0V691.54A25.6 25.6 0 0 1 512 716.8zm201.899-111.957c-51.2 0-92.843-36.523-92.843-81.579v-49.493a32.256 32.256 0 0 1 32.256-32.256h60.587c51.2 0 93.013 36.693 93.013 81.749s-41.813 81.579-93.013 81.579zm-41.643-112.128v30.549c0 17.067 19.115 30.379 41.643 30.379s41.813-13.824 41.813-30.379-19.115-30.55-41.813-30.55z"/><path fill="#1E50AE" d="M646.656 716.8a25.6 25.6 0 0 1-25.6-25.6V467.115a25.6 25.6 0 0 1 51.2 0V691.54a25.6 25.6 0 0 1-25.6 25.259zm-269.141 0H242.688a25.6 25.6 0 0 1-21.845-38.912l111.445-185.515h-89.6a25.6 25.6 0 0 1 0-51.2h134.827a25.6 25.6 0 0 1 21.845 38.742L287.915 665.6h89.6a25.6 25.6 0 0 1 0 51.2z"/></svg></a> 
            <a href="https://www.jyshare.com/more/svgeditor/" target="_blank" title="菜鸟svg编辑器"><svg width="32" height="32" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
					<!-- SVG图标，以层叠的山峦和太阳为主题 -->
					<!-- 定义渐变背景 -->
					<defs>
						<linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
							<stop offset="0%" style="stop-color:#87CEEB; stop-opacity:1" />
							<stop offset="100%" style="stop-color:#FFA07A; stop-opacity:1" />
						</linearGradient>
					</defs>
				
					<!-- 背景，使用圆角矩形模拟APP图标形状 -->
					<rect x="0" y="0" width="100" height="100" rx="22" ry="22" fill="url(#skyGradient)" />
				
					<!-- 太阳 -->
					<circle cx="50" cy="55" r="12" fill="#FFD700" opacity="0.8" />
				
					<!-- 前景山峦 -->
					<path d="M -5,70 Q 25,50 50,70 T 105,70 L 105,105 L -5,105 Z" fill="#2E8B57" opacity="0.9" />
				
					<!-- 中景山峦 -->
					<path d="M -5,80 Q 35,60 60,80 T 105,80 L 105,105 L -5,105 Z" fill="#3CB371" opacity="0.7" />
				
					<!-- 背景山峦 -->
					<path d="M -5,90 Q 20,75 50,90 T 105,90 L 105,105 L -5,105 Z" fill="#66CDAA" opacity="0.5" />
				</svg></a>
            </label>
            <textarea id="svgInput" placeholder="在此粘贴你的 SVG 代码..."></textarea>
        </div>

        <div class="section">
            <div class="tabs">
                <div class="tab active" data-tab="css">CSS 代码</div>
                <div class="tab" data-tab="dataUri">Data URI</div>
            </div>

            <div class="tab-content active" id="cssTab">
                <label for="cssOutput">CSS 背景代码：</label>
                <textarea id="cssOutput" readonly></textarea>
                <button id="copyCssBtn">复制 CSS 代码</button>

                <div class="options-group">
                    <label for="backgroundProperty">背景属性：</label>
                    <select id="backgroundProperty">
                        <option value="background-image">background-image</option>
                        <option value="background">background</option>
                    </select>

                    <label for="backgroundRepeat">重复方式：</label>
                    <select id="backgroundRepeat">
                        <option value="no-repeat">no-repeat</option>
                        <option value="repeat">repeat</option>
                        <option value="repeat-x">repeat-x</option>
                        <option value="repeat-y">repeat-y</option>
                        <option value="round">round</option>
                        <option value="space">space</option>
                    </select>

                    <label for="backgroundPosition">位置：</label>
                    <select id="backgroundPosition">
                        <option value="center">center</option>
                        <option value="top">top</option>
                        <option value="bottom">bottom</option>
                        <option value="left">left</option>
                        <option value="right">right</option>
                        <option value="left top">left top</option>
                        <option value="left center">left center</option>
                        <option value="left bottom">left bottom</option>
                        <option value="right top">right top</option>
                        <option value="right center">right center</option>
                        <option value="right bottom">right bottom</option>
                        <option value="center top">center top</option>
                        <option value="center bottom">center bottom</option>
                    </select>

                    <label for="backgroundSize">尺寸：</label>
                    <select id="backgroundSize">
                        <option value="auto">auto</option>
                        <option value="cover">cover</option>
                        <option value="contain">contain</option>
                        <option value="100% 100%">100% 100%</option>
                        <option value="50%">50%</option>
                    </select>
                </div>
            </div>

            <div class="tab-content" id="dataUriTab">
                <label for="dataUriOutput">Data URI：</label>
                <textarea id="dataUriOutput" readonly></textarea>
                <button id="copyDataUriBtn">复制 Data URI</button>
            </div>
        </div>

        <div class="section">
            <label>预览：</label>
            <div class="preview-controls">
                <input type="number" id="previewWidth" class="preview-size-input" value="500" min="10" max="1000">
                <span>×</span>
                <input type="number" id="previewHeight" class="preview-size-input" value="500" min="10" max="1000">
                <span>px</span>
                <button id="applySize">应用尺寸</button>
            </div>
            <div class="preview-box" id="previewBox"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const svgInput = document.getElementById('svgInput');
            const cssOutput = document.getElementById('cssOutput');
            const dataUriOutput = document.getElementById('dataUriOutput');
            const previewBox = document.getElementById('previewBox');
            const copyCssBtn = document.getElementById('copyCssBtn');
            const copyDataUriBtn = document.getElementById('copyDataUriBtn');
            const previewWidth = document.getElementById('previewWidth');
            const previewHeight = document.getElementById('previewHeight');
            const applySize = document.getElementById('applySize');
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            // 新增的选项元素
            const backgroundPropertySelect = document.getElementById('backgroundProperty');
            const backgroundRepeatSelect = document.getElementById('backgroundRepeat');
            const backgroundPositionSelect = document.getElementById('backgroundPosition'); // 修改为 Select
            const backgroundSizeSelect = document.getElementById('backgroundSize'); // 修改为 Select

            // 示例 SVG
            const exampleSvg = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <circle cx="12" cy="12" r="10"></circle>
  <line x1="12" y1="8" x2="12" y2="12"></line>
  <line x1="12" y1="16" x2="12.01" y2="16"></line>
</svg>`;
            svgInput.value = exampleSvg;

            // 初始化预览尺寸
            updatePreviewSize();

            // 转换 SVG 为 CSS 背景
            function convertSvgToCss(svg) {
                if (!svg.trim()) return '';

                // 替换双引号为单引号
                let processed = svg.replace(/"/g, '\'');
                
                // 处理特殊字符
                processed = processed.replace(/#/g, '%23');
                processed = processed.replace(/</g, '%3C');
                processed = processed.replace(/>/g, '%3E');
                processed = processed.replace(/\s+/g, ' ');
                processed = processed.replace(/[\r\n]+/g, ' ');
                
                const dataUri = `url("data:image/svg+xml,${processed}")`;
                
                const property = backgroundPropertySelect.value;
                const repeat = backgroundRepeatSelect.value;
                const position = backgroundPositionSelect.value; // 从 Select 获取值
                const size = backgroundSizeSelect.value; // 从 Select 获取值

                let cssCode = '';
                if (property === 'background-image') {
                    cssCode = `background-image: ${dataUri};\nbackground-repeat: ${repeat};\nbackground-position: ${position};\nbackground-size: ${size};`;
                } else { // background 简写
                    cssCode = `background: ${dataUri} ${repeat} ${position} / ${size};`;
                }
                
                return cssCode;
            }

            // 转换 SVG 为 Data URI
            function convertSvgToDataUri(svg) {
                if (!svg.trim()) return '';

                // 替换双引号为单引号
                let processed = svg.replace(/"/g, '\'');
                
                // 处理特殊字符
                processed = processed.replace(/#/g, '%23');
                processed = processed.replace(/</g, '%3C');
                processed = processed.replace(/>/g, '%3E');
                processed = processed.replace(/\s+/g, ' ');
                processed = processed.replace(/[\r\n]+/g, ' ');
                
                return `data:image/svg+xml,${processed}`;
            }

            // 更新输出和预览
            function updateOutput() {
                const svg = svgInput.value;
                const cssCode = convertSvgToCss(svg);
                const dataUri = convertSvgToDataUri(svg);
                
                cssOutput.value = cssCode;
                dataUriOutput.value = dataUri;
                
                // 更新预览
                const repeat = backgroundRepeatSelect.value;
                const position = backgroundPositionSelect.value; // 从 Select 获取值
                const size = backgroundSizeSelect.value; // 从 Select 获取值

                // 分别设置背景属性，避免 cssText 覆盖问题
                previewBox.style.backgroundImage = dataUri ? `url("${dataUri}")` : 'none'; // <-- 修改此处
                previewBox.style.backgroundRepeat = repeat;
                previewBox.style.backgroundPosition = position;
                previewBox.style.backgroundSize = size;
            }

            // 更新预览尺寸
            function updatePreviewSize() {
                const width = previewWidth.value + 'px';
                const height = previewHeight.value + 'px';
                previewBox.style.width = width;
                previewBox.style.height = height;
                updateOutput(); // 重新应用背景
            }

            // 复制到剪贴板
            function copyToClipboard(text, button) {
                navigator.clipboard.writeText(text).then(() => {
                    const originalText = button.textContent;
                    button.textContent = '已复制！';
                    setTimeout(() => {
                        button.textContent = originalText;
                    }, 2000);
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            }

            // 事件监听器
            svgInput.addEventListener('input', updateOutput);
            backgroundPropertySelect.addEventListener('change', updateOutput);
            backgroundRepeatSelect.addEventListener('change', updateOutput);
            backgroundPositionSelect.addEventListener('change', updateOutput); // 添加 Select 的事件监听
            backgroundSizeSelect.addEventListener('change', updateOutput); // 添加 Select 的事件监听
            
            copyCssBtn.addEventListener('click', () => {
                copyToClipboard(cssOutput.value, copyCssBtn);
            });
            
            copyDataUriBtn.addEventListener('click', () => {
                copyToClipboard(dataUriOutput.value, copyDataUriBtn);
            });
            
            applySize.addEventListener('click', updatePreviewSize);

            // 标签切换
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // 移除所有活动标签
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 激活当前标签
                    tab.classList.add('active');
                    const tabId = tab.getAttribute('data-tab');
                    const activeContent = tabId === 'css' ? document.getElementById('cssTab') : document.getElementById('dataUriTab');
                    activeContent.classList.add('active');
                });
            });

            // 初始化输出
            updateOutput();
        });
    </script>
</body>
</html>