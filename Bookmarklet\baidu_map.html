<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>今天中午吃什么</title>
    <style type="text/css">
        body, html, #allmap {width: 100%;height: 100%;overflow: hidden;margin:0;font-family:"微软雅黑";}
        #panel {position: absolute;top:10px;left:10px;width:280px;max-height:90%;overflow-y:auto;background-color:white;padding:10px;border:1px solid #ccc;z-index:1;}
        /* 新增：关闭按钮样式 */
        #close-panel-btn {
            position: absolute;
            top: 5px;
            right: 8px;
            background: none;
            border: none;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            padding: 0;
            line-height: 1;
            color: #888;
        }
        #close-panel-btn:hover {
            color: #000;
        }
        /* 新增：手动定位按钮样式 */
        #manual-location-btn {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        #manual-location-btn:hover {
            background-color: #f0f0f0;
        }
        /* 新增：定位模式提示样式 */
        #location-mode-hint {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 16px;
            z-index: 1000;
            display: none;
        }
        ul {
            list-style-type:none;
            padding-left: 0;     /* 移除左内边距 */
            margin-left: 0;      /* 同时移除左外边距，以防万一 */
        }
        li {list-style-type: none; padding-bottom: 5px; border-bottom: 1px dashed #eee; margin-bottom: 5px;}
        li:last-child {border-bottom: none;}
        /* 为地址和电话图标添加样式 */
        .icon-address, .icon-phone {
            display: block;
            margin-top: 3px;
        }
        /* 新增样式：到这里去 链接 */
        .go-here-link {
            display: inline-block;
            margin-top: 5px;
            color: #007bff;
            cursor: pointer;
            text-decoration: underline;
        }
        /* 新增：悬浮打开按钮样式 */
        #open-panel-btn {
            position: fixed; /* 使用 fixed 定位，使其悬浮 */
            top: 10px;
            left: 10px;
            z-index: 1001; /* 比 panel 的 z-index 高，确保在 panel 隐藏时可见 */
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            display: none; /* 初始状态隐藏 */
            font-size: 18px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .location-icon {
            cursor: move; /* 显示可拖动的光标 */
        }
        /* 新增：响应式设计 - 手机版隐藏地址和电话 */
        @media (max-width: 768px) {
            #panel {
                width: 200px;
            }
            .icon-address, .icon-phone {
                display: none; /* 在小屏幕上隐藏地址和电话 */
            }
            /* 可以考虑调整字体大小等 */
            #restaurant-list li {
                font-size: 14px; /* 示例：缩小字体 */
            }
            #restaurant-list .search-info {
                font-size: 15px; /* 示例：缩小搜索信息字体 */
            }
             h3 {
                font-size: 16px; /* 示例：缩小标题字体 */
            }
        }
    </style>
</head>
<body>
    <div id="allmap"></div>
    <div id="panel">
        <button id="close-panel-btn" title="关闭列表">&times;</button>
        <h3>周边美食列表：</h3>
        <ul id="restaurant-list"></ul>
    </div>
    <button id="open-panel-btn" title="打开列表">☰</button>
    <!-- 新增：手动定位按钮 -->
    <button id="manual-location-btn" title="手动定位">📍 手动定位</button>
    <!-- 新增：定位模式提示 -->
    <div id="location-mode-hint">拖动地图调整位置，点击地图确认新位置</div>

    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=j7cQnNNo6gupeurXWTcQiKRJHZkyHPVF&callback=initMap"></script>
    <script type="text/javascript">
        // 定义一个全局变量来存储当前位置点，方便后续路线规划使用
        var currentPositionPoint = null;
        // 新增：定义一个全局变量来存储当前的步行路线规划实例
        var currentWalkingRoute = null;
        // 新增：定义全局变量存储地图实例和当前位置标记
        var map = null;
        var currentPositionMarker = null;
        // 新增：定义手动定位模式标志
        var isManualLocationMode = false;

        // 百度地图API功能
        function initMap() {
            // 1. 创建地图实例
            map = new BMap.Map("allmap");
            map.enableScrollWheelZoom(true); // 开启鼠标滚轮缩放

            // 2. 获取当前位置
            var geolocation = new BMap.Geolocation();
            geolocation.getCurrentPosition(function(r){
                if(this.getStatus() == BMAP_STATUS_SUCCESS){
                    // 创建自定义定位图标
                    var locationIcon = new BMap.Icon(
                    "data:image/svg+xml;charset=UTF-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 24 24'%3E%3Ccircle cx='12' cy='12' r='10' fill='%23007bff' stroke='white' stroke-width='2'/%3E%3Ccircle cx='12' cy='12' r='3' fill='white'/%3E%3C/svg%3E",
                    new BMap.Size(30, 30), // 图标大小
                        {
                            imageSize: new BMap.Size(30, 30), // 图片大小
                            anchor: new BMap.Size(15, 15) // 锚点位置
                        }
                    );
                    
                    // 创建可拖动的标记
                    currentPositionMarker = new BMap.Marker(r.point, {
                        icon: locationIcon,
                        enableDragging: true, // 启用拖拽
                        raiseOnDrag: true // 拖拽时置于其他标记之上
                    });
                    
                    // 添加拖拽结束事件
                    currentPositionMarker.addEventListener("dragend", function(e) {
                        // 更新当前位置点
                        currentPositionPoint = e.point;
                        // 重新搜索周边餐厅
                        searchNearbyRestaurants(map, e.point);
                    });
                    
                    // 为定位标记添加自定义样式类
                    currentPositionMarker.setTitle("当前位置 (可拖动)");
                    
                    map.addOverlay(currentPositionMarker); // 将标注添加到地图中
                    map.centerAndZoom(r.point, 20); // 初始化地图,设置中心点坐标和地图级别
                    console.log('您的位置：'+r.point.lng+','+r.point.lat);
                    currentPositionPoint = r.point; // 保存当前位置点

                    // 3. 获取周边的餐厅
                    searchNearbyRestaurants(map, r.point);
                }
                else {
                    alert('定位失败: '+this.getStatus());
                    // 定位失败，可以设置一个默认的中心点，例如北京
                    var defaultPoint = new BMap.Point(116.404, 39.915);
                    map.centerAndZoom(defaultPoint, 15);
                    currentPositionPoint = defaultPoint;
                    
                    // 创建自定义定位图标
                    var locationIcon = new BMap.Icon(
                        "https://api.map.baidu.com/images/geolocation-control/position-icon-14x14.png",
                        new BMap.Size(30, 30),
                        {
                            imageSize: new BMap.Size(30, 30),
                            anchor: new BMap.Size(15, 15)
                        }
                    );
                    
                    // 添加默认位置标记（可拖动）
                    currentPositionMarker = new BMap.Marker(defaultPoint, {
                        icon: locationIcon,
                        enableDragging: true,
                        raiseOnDrag: true
                    });
                    
                    // 添加拖拽结束事件
                    currentPositionMarker.addEventListener("dragend", function(e) {
                        // 更新当前位置点
                        currentPositionPoint = e.point;
                        // 重新搜索周边餐厅
                        searchNearbyRestaurants(map, e.point);
                    });
                    
                    currentPositionMarker.setTitle("当前位置 (可拖动)");
                    map.addOverlay(currentPositionMarker);
                }
            },{enableHighAccuracy: true});

            // 添加地图控件
            map.addControl(new BMap.NavigationControl()); // 平移缩放控件
            map.addControl(new BMap.ScaleControl()); // 比例尺控件
            map.addControl(new BMap.OverviewMapControl()); // 缩略地图控件
            map.addControl(new BMap.MapTypeControl()); // 地图类型控件
            
            // 新增：添加地图点击事件监听器（用于手动定位模式）
            map.addEventListener('click', function(e) {
                if (isManualLocationMode) {
                    // 更新当前位置点
                    updateCurrentPosition(e.point);
                    // 退出手动定位模式
                    exitManualLocationMode();
                }
            });
        }
        
        // 新增：进入手动定位模式
        function enterManualLocationMode() {
            isManualLocationMode = true;
            // 显示提示信息
            document.getElementById('location-mode-hint').style.display = 'block';
            // 更改按钮文本
            document.getElementById('manual-location-btn').textContent = '📍 点击确认位置';
            
            // 确保定位标记可拖动
            if (currentPositionMarker) {
                currentPositionMarker.enableDragging();
                // 闪烁效果提示用户可拖动
                blinkMarker(currentPositionMarker);
            }
            
            // 添加定时器，5秒后自动隐藏提示
            setTimeout(function() {
                document.getElementById('location-mode-hint').style.display = 'none';
                // 注意：这里只隐藏提示，不退出手动定位模式
            }, 5000); // 5000毫秒 = 5秒
        }
        
        // 新增：退出手动定位模式
        function exitManualLocationMode() {
            isManualLocationMode = false;
            // 隐藏提示信息
            document.getElementById('location-mode-hint').style.display = 'none';
            // 恢复按钮文本
            document.getElementById('manual-location-btn').textContent = '📍 手动定位';
        }
        
        // 新增：标记闪烁效果
        function blinkMarker(marker) {
            var count = 0;
            var maxCount = 6; // 闪烁3次
            var interval = setInterval(function() {
                if (count >= maxCount) {
                    clearInterval(interval);
                    return;
                }
                
                if (count % 2 === 0) {
                    marker.hide();
                } else {
                    marker.show();
                }
                
                count++;
            }, 300);
        }
        
        // 新增：更新当前位置
        function updateCurrentPosition(point) {
            // 更新全局变量
            currentPositionPoint = point;
            
            // 移动现有标记到新位置
            if (currentPositionMarker) {
                currentPositionMarker.setPosition(point);
            } else {
                // 如果标记不存在，创建一个新的
                var locationIcon = new BMap.Icon(
                    "https://api.map.baidu.com/images/geolocation-control/position-icon-14x14.png",
                    new BMap.Size(30, 30),
                    {
                        imageSize: new BMap.Size(30, 30),
                        anchor: new BMap.Size(15, 15)
                    }
                );
                
                currentPositionMarker = new BMap.Marker(point, {
                    icon: locationIcon,
                    enableDragging: true,
                    raiseOnDrag: true
                });
                
                currentPositionMarker.addEventListener("dragend", function(e) {
                    currentPositionPoint = e.point;
                    searchNearbyRestaurants(map, e.point);
                });
                
                currentPositionMarker.setTitle("当前位置 (可拖动)");
                map.addOverlay(currentPositionMarker);
            }
            
            // 重新搜索周边餐厅
            searchNearbyRestaurants(map, point);
        }

        // 新增的函数：开始步行路线规划
        function startWalkingRoute(map, startPoint, endPoint) {
            if (!startPoint) {
                alert('无法获取当前位置，无法规划路线。');
                return;
            }

            // 清除之前的路线规划结果
            if (currentWalkingRoute) {
                currentWalkingRoute.clearResults();
            }

            var walking = new BMap.WalkingRoute(map, {
                renderOptions:{map: map, autoViewport: true},
                onSearchComplete: function(results){
                    if (walking.getStatus() == BMAP_STATUS_SUCCESS){
                        // 可以在这里处理路线规划成功后的逻辑，例如显示总距离和时间
                        console.log("步行路线规划成功！");
                    } else {
                        console.log("步行路线规划失败：" + walking.getStatus());
                        alert("步行路线规划失败，请稍后再试。");
                    }
                },
                onMarkersSet: function(pois){
                    // 当标注设置完毕时触发，可用于自定义标注
                },
                onPolylinesSet: function(routes){
                    // 当路线绘制完毕时触发，可用于自定义路线样式
                }
            });
            walking.search(startPoint, endPoint);
            // 保存当前的路线规划实例
            currentWalkingRoute = walking;
        }

        function searchNearbyRestaurants(map, centerPoint) {
            // 标记是否已经为本次搜索操作尝试过随机翻页
            var hasAttemptedRandomPageForThisSearch = false;

            // 1. 随机化搜索半径 (例如：500米到1000米之间)
            var minRadius = 500; // 您代码中的值
            var maxRadius = 1000;
            var randomRadius = Math.floor(Math.random() * (maxRadius - minRadius + 1)) + minRadius;

            // 添加随机关键词功能
            var foodKeywords = ["美食", "餐厅", "中餐", "快餐", "江湖菜", "小吃", "面馆", "饭店", "川菜"];
            var randomKeywordIndex = Math.floor(Math.random() * foodKeywords.length);
            var searchKeyword = foodKeywords[randomKeywordIndex];

            // 在控制台显示本次搜索的关键词（方便调试）
            console.log("本次搜索关键词: " + searchKeyword);

            var options = {
                onSearchComplete: function(results){
                    var restaurantListEl = document.getElementById('restaurant-list');
                    restaurantListEl.innerHTML = ''; // 清空之前的列表

                    // 显示当前搜索的关键词
                    var searchInfoEl = document.createElement('div');
                    searchInfoEl.className = 'search-info';
                    searchInfoEl.innerHTML = '当前搜索: <strong>' + searchKeyword + '</strong>';
                    restaurantListEl.appendChild(searchInfoEl);

                    if (local.getStatus() == BMAP_STATUS_SUCCESS){
                        var numPages = results.getNumPages();
                        var currentPageIndex = results.getPageIndex();

                        // 2. 如果是首次回调、有多页结果，并且尚未尝试随机翻页
                        if (!hasAttemptedRandomPageForThisSearch && numPages > 1) {
                            hasAttemptedRandomPageForThisSearch = true; // 标记已尝试
                            var targetPageIndex = Math.floor(Math.random() * numPages); // 随机选择一个目标页码

                            // 如果随机选择的页不是当前页，则跳转
                            if (targetPageIndex !== currentPageIndex) {
                                local.gotoPage(targetPageIndex); // 这会再次触发 onSearchComplete
                                return; // 等待新页面的结果
                            }
                            // 如果随机选中的就是当前页，则继续处理当前页数据
                        }

                        // 获取当前页的POI数据
                        var allPois = [];
                        if (results.getCurrentNumPois() > 0) {
                            for (var i = 0; i < results.getCurrentNumPois(); i ++){
                                allPois.push(results.getPoi(i));
                            }
                        }

                        if (allPois.length === 0) {
                            var li = document.createElement('li');
                            // 为“无结果”提示添加class
                            li.className = 'no-results';
                            var message = hasAttemptedRandomPageForThisSearch ? '此区域未找到更多可吃饭的地方。' : '附近未找到可吃饭的地方。';
                            li.appendChild(document.createTextNode(message));
                            restaurantListEl.appendChild(li);
                            return;
                        }

                        // 3. 打乱当前页的POI点数组 (Fisher-Yates Shuffle)
                        for (var i = allPois.length - 1; i > 0; i--) {
                            var j = Math.floor(Math.random() * (i + 1));
                            var temp = allPois[i];
                            allPois[i] = allPois[j];
                            allPois[j] = temp;
                        }

                        // 4. 遍历打乱后的POI点并显示
                        for (var i = 0; i < allPois.length; i ++){
                            var poi = allPois[i];

                            // 在地图上添加标注
                            var marker = new BMap.Marker(poi.point);
                            map.addOverlay(marker);

                            // 构建信息窗口内容，添加“到这里去”链接
                            var infoWindowContent = "<strong>" + poi.title + "</strong><br>地址: " + poi.address;
                            if (poi.phoneNumber) {
                                infoWindowContent += "<br>电话: " + poi.phoneNumber;
                            }
                            // 添加“到这里去”链接，注意这里不需要data-lng和data-lat了，因为我们会在打开信息窗口时动态绑定
                            infoWindowContent += '<br><a href="#" class="go-here-link">到这里去 (步行)</a>';

                            // 为每个POI创建独立的信息窗口实例
                            var infoWindow = new BMap.InfoWindow(infoWindowContent);

                            // marker点击事件：使用闭包确保每个marker对应正确的infoWindow和poi.point
                            marker.addEventListener("click", (function(currentPoint, currentInfoWindow, currentPoi){
                                return function(){
                                    map.openInfoWindow(currentInfoWindow, currentPoint);
                                    // 在信息窗口打开后，获取并绑定“到这里去”链接的事件
                                    // 确保信息窗口内容已经渲染到DOM中
                                    var contentDiv = document.querySelector('.BMap_bubble_content'); // 百度地图信息窗口内容的通用类名
                                    if (contentDiv) {
                                        var goHereLink = contentDiv.querySelector('.go-here-link');
                                        if (goHereLink) {
                                            goHereLink.onclick = function(event) {
                                                event.preventDefault();
                                                startWalkingRoute(map, currentPositionPoint, currentPoi.point); // 使用当前POI的point
                                                map.closeInfoWindow(); // 关闭信息窗口
                                            };
                                        }
                                    }
                                };
                            })(poi.point, infoWindow, poi)); // 将整个poi对象也传入闭包，以便获取其point

                            // 在列表中显示餐厅信息
                            var li = document.createElement('li');

                            // 创建可点击的餐厅标题
                            var titleLink = document.createElement('a');
                            titleLink.href = "#"; // 避免页面跳转
                            titleLink.innerHTML = "<strong>" + poi.title + "</strong>";

                            // 为标题链接添加点击事件：使用闭包确保正确引用poi.point和infoWindow
                            titleLink.addEventListener('click', (function(currentPoint, currentInfoWindow, currentPoi) {
                                return function(event) {
                                    event.preventDefault(); // 阻止 <a> 标签的默认行为
                                    map.panTo(currentPoint); // 将地图中心平移到POI点
                                    map.openInfoWindow(currentInfoWindow, currentPoint); // 打开信息窗口

                                    // 在信息窗口打开后，获取并绑定“到这里去”链接的事件
                                    // 确保信息窗口内容已经渲染到DOM中
                                    var contentDiv = document.querySelector('.BMap_bubble_content'); // 百度地图信息窗口内容的通用类名
                                    if (contentDiv) {
                                        var goHereLink = contentDiv.querySelector('.go-here-link');
                                        if (goHereLink) {
                                            goHereLink.onclick = function(event) {
                                                event.preventDefault();
                                                startWalkingRoute(map, currentPositionPoint, currentPoi.point); // 使用当前POI的point
                                                map.closeInfoWindow(); // 关闭信息窗口
                                            };
                                        }
                                    }
                                };
                            })(poi.point, infoWindow, poi)); // 将整个poi对象也传入闭包，以便获取其point

                            li.appendChild(titleLink); // 先添加标题链接

                            // 创建包含地址和电话的div
                            var poiInfoDiv = document.createElement('div');
                            poiInfoDiv.className = 'poi-info';

                            var addressSpan = document.createElement('span');
                            addressSpan.className = 'icon-address'; // 添加class以显示图标
                            addressSpan.appendChild(document.createTextNode("📍 " + poi.address)); // 添加地址图标
                            poiInfoDiv.appendChild(addressSpan);

                            if (poi.phoneNumber) {
                                var phoneSpan = document.createElement('span');
                                phoneSpan.className = 'icon-phone'; // 添加class以显示图标
                                phoneSpan.appendChild(document.createTextNode("📞 " + poi.phoneNumber)); // 添加电话图标
                                poiInfoDiv.appendChild(phoneSpan);
                            }

                            // 将包含详细信息的div添加到标题链接之后（或li中）
                            li.appendChild(poiInfoDiv);

                            restaurantListEl.appendChild(li);
                        }

                        // 在所有POI加载完毕后，为所有的“到这里去”链接添加事件监听器
                        // 使用事件委托来处理动态生成的内容
                        map.addEventListener('infowindowopen', function(e) {
                            var infoWindowContentElement = e.target.getContent(); // 获取信息窗口的DOM元素
                            // 查找信息窗口内容中的所有 .go-here-link 元素
                            var goHereLinks = infoWindowContentElement.querySelectorAll('.go-here-link');
                            goHereLinks.forEach(function(link) {
                                link.onclick = function(event) {
                                    event.preventDefault();
                                    var targetLng = parseFloat(this.getAttribute('data-lng'));
                                    var targetLat = parseFloat(this.getAttribute('data-lat'));
                                    var endPoint = new BMap.Point(targetLng, targetLat);
                                    startWalkingRoute(map, currentPositionPoint, endPoint); // 调用步行路线规划函数
                                    map.closeInfoWindow(); // 关闭信息窗口
                                };
                            });
                        });


                    } else {
                        var li = document.createElement('li');
                        li.className = 'no-results'; // 为错误提示也添加class
                        li.appendChild(document.createTextNode('搜索美食地点失败。'));
                        restaurantListEl.appendChild(li);
                    }
                }
            };
            var local = new BMap.LocalSearch(map, options);
            // 使用随机化后的关键词和半径进行搜索
            local.searchNearby(searchKeyword, centerPoint, randomRadius);
            // console.log("本次搜索半径: " + randomRadius + "米"); // 方便调试
        }

        // 由于API是异步加载的，initMap函数需要全局可见
        window.initMap = initMap;

        // 新增：面板显隐控制逻辑
        document.addEventListener('DOMContentLoaded', function() {
            var panel = document.getElementById('panel');
            var closePanelBtn = document.getElementById('close-panel-btn');
            var openPanelBtn = document.getElementById('open-panel-btn');
            var manualLocationBtn = document.getElementById('manual-location-btn');

            if (closePanelBtn) {
                closePanelBtn.addEventListener('click', function() {
                    panel.style.display = 'none';
                    openPanelBtn.style.display = 'block';
                });
            }

            if (openPanelBtn) {
                openPanelBtn.addEventListener('click', function() {
                    panel.style.display = 'block';
                    openPanelBtn.style.display = 'none';
                });
            }
            
            // 新增：手动定位按钮点击事件
            if (manualLocationBtn) {
                manualLocationBtn.addEventListener('click', function() {
                    if (isManualLocationMode) {
                        // 如果已经在手动定位模式，点击按钮则确认当前地图中心为新位置
                        updateCurrentPosition(map.getCenter());
                        exitManualLocationMode();
                    } else {
                        // 进入手动定位模式
                        enterManualLocationMode();
                    }
                });
            }
        });
    </script>
</body>
</html>