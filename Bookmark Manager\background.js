// 用于存储书签URL的集合，方便快速查找
let bookmarkedUrls = new Set();
// 存储原始URL到重定向URL的映射
let redirectMap = {};

// 辅助函数：更新书签的点击统计和最后点击时间
async function updateBookmarkStats(urlToUpdate) {
  try {
    const stats = await chrome.storage.local.get(urlToUpdate);
    const currentCount = stats[urlToUpdate]?.count || 0;
    const newCount = currentCount + 1;
    await chrome.storage.local.set({
      [urlToUpdate]: {
        count: newCount,
        lastClicked: new Date().getTime()
      }
    });
    console.log(`已更新书签点击统计: ${urlToUpdate}, 点击次数: ${newCount}`);
  } catch (e) {
    console.error(`更新书签 ${urlToUpdate} 统计时出错:`, e);
  }
}

// 监听webNavigation事件来跟踪导航的起始URL
chrome.webNavigation.onBeforeNavigate.addListener(details => {
  if (details.frameId === 0) { // 只跟踪主框架
    redirectMap[details.tabId] = { originalUrl: details.url };
  }
});

// 跟踪导航完成事件，处理书签访问和重定向
chrome.webNavigation.onCompleted.addListener(async details => {
  // 只处理主框架的导航，并且是我们已经开始跟踪的导航
  if (details.frameId === 0 && redirectMap[details.tabId]) {
    const originalUrl = redirectMap[details.tabId].originalUrl;
    const finalUrl = details.url;

    // 清理映射表中的对应条目，防止内存泄漏或错误处理
    delete redirectMap[details.tabId];

    try {
      if (originalUrl !== finalUrl) {
        // 情况1: 发生了重定向 (originalUrl 与 finalUrl 不同)
        if (bookmarkedUrls.has(originalUrl)) {
          // 如果原始URL是一个书签，则更新这个原始书签的统计
          console.log(`检测到书签重定向: ${originalUrl} -> ${finalUrl}`);
          await updateBookmarkStats(originalUrl);
        } else {
          // 如果原始URL不是书签，但最终URL是一个书签 (例如：非书签页重定向到书签页)
          if (bookmarkedUrls.has(finalUrl)) {
            console.log(`非书签 ${originalUrl} 重定向到书签: ${finalUrl}`);
            await updateBookmarkStats(finalUrl);
          }
        }
      } else {
        // 情况2: 没有发生重定向 (originalUrl 与 finalUrl 相同)
        // 这意味着直接访问了 finalUrl (也就是 originalUrl)
        if (bookmarkedUrls.has(finalUrl)) {
          console.log(`直接访问书签: ${finalUrl}`);
          await updateBookmarkStats(finalUrl);
        }
      }
    } catch (e) {
      console.error("处理导航完成逻辑时出错:", e);
    }
  }
});

// 当标签关闭时清理映射
chrome.tabs.onRemoved.addListener(tabId => {
  if (redirectMap[tabId]) {
    delete redirectMap[tabId];
    console.log(`标签 ${tabId} 关闭，已清理 redirectMap 条目。`);
  }
});

// 监听书签变动，保持 bookmarkedUrls 最新
chrome.bookmarks.onCreated.addListener(async (id, bookmark) => {
  console.log("书签已创建:", bookmark.url);
  // bookmarkedUrls.add(bookmark.url) 会在 initializeBookmarks 中处理
  await initializeBookmarks(); // 重新初始化以确保数据一致性
});

chrome.bookmarks.onRemoved.addListener(async (id, removeInfo) => {
  console.log("书签已移除:", removeInfo.node.url);
  // 注意: removeInfo.node.url 可能不存在（如果是文件夹）
  // 从存储中移除统计数据（可选）
  if (removeInfo.node.url) {
    try {
      await chrome.storage.local.remove(removeInfo.node.url);
      console.log("已移除书签统计:", removeInfo.node.url);
    } catch (e) {
      console.error("移除书签统计时出错:", e);
    }
  }
  await initializeBookmarks(); // 重新初始化
});

chrome.bookmarks.onChanged.addListener(async (id, changeInfo) => {
  console.log("书签已更改:", id, changeInfo);
  await initializeBookmarks(); // 重新初始化
});

// 扩展安装或启动时初始化
chrome.runtime.onStartup.addListener(initializeBookmarks);
chrome.runtime.onInstalled.addListener(initializeBookmarks);

// 初始化书签URL集合的函数
async function initializeBookmarks() {
  try {
    const bookmarkTreeNodes = await chrome.bookmarks.getTree();
    const newBookmarkedUrls = new Set(); // 使用新的Set进行构建，然后替换旧的
    function parseBookmarkNodes(nodes) {
      for (const node of nodes) {
        if (node.url) {
          // 确保只添加有效的HTTP/HTTPS URL
          if (node.url.startsWith('http:') || node.url.startsWith('https:')) {
            newBookmarkedUrls.add(node.url);
          }
        }
        if (node.children) {
          parseBookmarkNodes(node.children);
        }
      }
    }
    parseBookmarkNodes(bookmarkTreeNodes);
    bookmarkedUrls = newBookmarkedUrls; // 原子性地替换整个集合
    console.log('书签URL集合已更新, 数量:', bookmarkedUrls.size);
  } catch (e) {
    console.error('初始化书签URL集合时出错:', e);
  }
}

// 立即执行一次初始化
initializeBookmarks();