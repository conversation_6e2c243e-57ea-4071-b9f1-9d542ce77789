<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机视频</title>
    <style>
        body {
            background-color: black;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        .video-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            margin: 0;
        }

        .video-element {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: opacity 0.5s ease;
            background-color: black;
        }
        .hidden {
            opacity: 0;
            z-index: 1;
        }
        .visible {
            opacity: 1;
            z-index: 2;
        }
        #refreshBtn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 5px 15px;
            background-color: rgba(51, 51, 51, 0.8);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 4;
            font-size: 14px;
        }
        #refreshBtn:hover {
            background-color: rgba(85, 85, 85, 0.9);
            transform: scale(1.05);
        }
        #playButton {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background-color: rgba(51, 51, 51, 0.8);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            z-index: 3;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            transition: all 0.3s ease;
        }

        #playButton:hover {
            background-color: rgba(85, 85, 85, 0.8);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .play-icon {
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 15px 0 15px 25px;
            border-color: transparent transparent transparent #ffffff;
            margin-left: 5px;
        }
    </style>
</head>
<!-- 随机视频合集：https://iui.su/2731/ -->
<body>
    <div class="video-container">
        <button id="playButton"><div class="play-icon"></div></button>
        <video id="videoA" class="video-element visible" playsinline webkit-playsinline preload="auto" controls>
        </video>
        <video id="videoB" class="video-element hidden" playsinline webkit-playsinline preload="auto" controls>
        </video>
        <button id="refreshBtn">刷新</button>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const videoA = document.getElementById('videoA');
            const videoB = document.getElementById('videoB');
            const refreshBtn = document.getElementById('refreshBtn');
            const playButton = document.getElementById('playButton');
            let currentVideo = videoA;
            let nextVideo = videoB;
            let isLoading = false;
            let wheelTimeout = null;
            let videoHistory = [];
            let currentHistoryIndex = -1;

            document.addEventListener('wheel', function(event) {
                if (wheelTimeout) {
                    clearTimeout(wheelTimeout);
                }
                wheelTimeout = setTimeout(() => {
                    if (event.deltaY < 0) {
                        console.log('向上滚动，尝试加载上一个视频'); // 用于调试
                        loadPreviousVideo();
                    } else {
                        console.log('向下滚动，加载新视频'); // 用于调试
                        refreshVideo();
                    }
                }, 200);
            });

            function showLoading() {
                isLoading = true;
            }
            
            function hideLoading() {
                isLoading = false;
            }

            function handleVideoError() {
                console.error('视频加载失败，尝试重新加载');
                hideLoading();
                refreshVideo();
            }

            function switchVideos() {
                if (nextVideo.readyState >= 3) {
                    currentVideo.classList.remove('visible');
                    currentVideo.classList.add('hidden');
                    nextVideo.classList.remove('hidden');
                    nextVideo.classList.add('visible');
                    
                    currentVideo.pause();
                    currentVideo.currentTime = 0;
                    
                    // 切换 currentVideo 和 nextVideo 的引用
                    const temp = currentVideo;
                    currentVideo = nextVideo;
                    nextVideo = temp;

                    hideLoading();
                }
            }

            function addToHistory(videoSrc) {
                // 如果是从历史记录加载的视频，不要重复添加
                if (videoHistory[currentHistoryIndex] !== videoSrc) {
                    // 删除当前索引之后的所有记录（如果有）
                    videoHistory = videoHistory.slice(0, currentHistoryIndex + 1);
                    videoHistory.push(videoSrc);
                    currentHistoryIndex = videoHistory.length - 1;
                    
                    // 限制历史记录最大数量为20条
                    if (videoHistory.length > 20) {
                        videoHistory.shift();
                        currentHistoryIndex--;
                    }
                }
                console.log('History:', videoHistory, 'Current Index:', currentHistoryIndex); // 用于调试
            }

            function loadPreviousVideo() {
                if (isLoading) return;
                
                // 确保有历史记录可以回退
                if (currentHistoryIndex <= 0) {
                    console.log('没有更早的历史记录了');
                    return;
                }
                
                showLoading();
                try {
                    currentHistoryIndex--;
                    const previousSrc = videoHistory[currentHistoryIndex];
                    console.log('Loading previous video:', previousSrc, 'Index:', currentHistoryIndex); // 用于调试
                    
                    nextVideo.src = previousSrc;
                    nextVideo.load();
                    
                    nextVideo.onloadeddata = function() {
                        nextVideo.play()
                            .then(() => {
                                switchVideos();
                                addToHistory(previousSrc); // 确保添加到历史记录
                            })
                            .catch(error => {
                                console.error('播放失败:', error);
                                hideLoading();
                            });
                    };
                } catch(error) {
                    console.error('加载上一个视频时出错:', error);
                    hideLoading();
                }
            }

            function refreshVideo() {
                if (isLoading) return;
                
                showLoading();
                try {
                    // 先记录当前视频的URL到历史记录
                    if (currentVideo.src && currentVideo.src !== "") {
                        addToHistory(currentVideo.src);
                    }

                    const baseUrl = 'https://api.yujn.cn/api/zzxjj.php?type=video';
                    const newTimestamp = Date.now();
                    const newSrc = `${baseUrl}&t=${newTimestamp}`;
                    
                    nextVideo.src = newSrc;
                    nextVideo.load();
                    
                    nextVideo.onloadeddata = function() {
                        nextVideo.play()
                            .then(() => {
                                switchVideos();
                            })
                            .catch(error => {
                                console.error('播放失败:', error);
                                hideLoading();
                            });
                    };
                } catch(error) {
                    console.error('刷新视频时出错:', error);
                    hideLoading();
                }
            }

            async function initializePlayback() {
                try {
                    await currentVideo.play();
                    playButton.style.display = 'none';
                } catch (error) {
                    console.error('自动播放失败:', error);
                }
            }

            [videoA, videoB].forEach(video => {
                video.addEventListener('error', handleVideoError);
                video.addEventListener('ended', refreshVideo);
                video.addEventListener('loadstart', showLoading);
                video.addEventListener('canplay', hideLoading);
            });

            refreshBtn.addEventListener('click', refreshVideo);
            playButton.addEventListener('click', initializePlayback);
            
            const initialSrc = `https://api.yujn.cn/api/zzxjj.php?type=video&t=${Date.now()}`;
            videoA.src = initialSrc;
            videoA.load();
            videoA.onloadeddata = function() {
                addToHistory(initialSrc); // 只在初始视频加载成功后添加到历史记录
            };
            videoA.removeAttribute('autoplay');
            videoB.removeAttribute('autoplay');
        });
    </script>
</body>
</html>