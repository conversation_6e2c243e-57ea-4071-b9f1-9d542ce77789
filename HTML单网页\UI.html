<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern UI Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }
        
        .sidebar {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        }
        
        .chart-container {
            height: 300px;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #c7d2fe;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a5b4fc;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Sidebar -->
        <div class="sidebar bg-white w-64 border-r border-gray-200 flex flex-col">
            <div class="p-4 flex items-center justify-between border-b border-gray-200">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 rounded-full gradient-bg flex items-center justify-center">
                        <i class="fas fa-rocket text-white"></i>
                    </div>
                    <span class="text-xl font-semibold text-gray-800">Nexus</span>
                </div>
                <button class="text-gray-500 hover:text-gray-700 lg:hidden">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            
            <div class="flex-1 overflow-y-auto py-4">
                <nav>
                    <div class="px-4 mb-6">
                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Main</p>
                    </div>
                    <ul>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-indigo-600 bg-indigo-50 mx-2 rounded-lg">
                                <i class="fas fa-home mr-3 text-indigo-600"></i>
                                <span class="font-medium">Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 mx-2 rounded-lg">
                                <i class="fas fa-chart-line mr-3"></i>
                                <span class="font-medium">Analytics</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 mx-2 rounded-lg">
                                <i class="fas fa-envelope mr-3"></i>
                                <span class="font-medium">Messages</span>
                                <span class="ml-auto bg-indigo-100 text-indigo-800 text-xs font-medium px-2 py-0.5 rounded-full">5</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 mx-2 rounded-lg">
                                <i class="fas fa-calendar mr-3"></i>
                                <span class="font-medium">Calendar</span>
                            </a>
                        </li>
                    </ul>
                    
                    <div class="px-4 mt-8 mb-6">
                        <p class="text-xs font-semibold text-gray-500 uppercase tracking-wider">Settings</p>
                    </div>
                    <ul>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 mx-2 rounded-lg">
                                <i class="fas fa-cog mr-3"></i>
                                <span class="font-medium">Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="flex items-center px-4 py-3 text-gray-600 hover:text-indigo-600 hover:bg-indigo-50 mx-2 rounded-lg">
                                <i class="fas fa-question-circle mr-3"></i>
                                <span class="font-medium">Help</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            
            <div class="p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-10 h-10 rounded-full">
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-900">Sarah Johnson</p>
                        <p class="text-xs text-gray-500">Admin</p>
                    </div>
                    <button class="ml-auto text-gray-500 hover:text-gray-700">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 overflow-auto">
            <!-- Header -->
            <header class="bg-white border-b border-gray-200 py-4 px-6 flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-gray-800">Dashboard</h1>
                    <div class="ml-6 relative">
                        <input type="text" placeholder="Search..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700 relative">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </button>
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-envelope text-xl"></i>
                    </button>
                    <div class="w-px h-8 bg-gray-200"></div>
                    <div class="flex items-center">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User" class="w-8 h-8 rounded-full">
                        <span class="ml-2 text-sm font-medium text-gray-700 hidden md:inline">Sarah Johnson</span>
                        <i class="fas fa-chevron-down ml-1 text-gray-500 text-xs"></i>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <main class="p-6">
                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-sm p-6 card-hover transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                                <p class="text-2xl font-bold text-gray-800 mt-1">$24,780</p>
                                <p class="text-xs text-green-500 mt-1 flex items-center">
                                    <i class="fas fa-arrow-up mr-1"></i> 12.5% from last month
                                </p>
                            </div>
                            <div class="bg-indigo-100 p-3 rounded-lg">
                                <i class="fas fa-dollar-sign text-indigo-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 card-hover transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">New Users</p>
                                <p class="text-2xl font-bold text-gray-800 mt-1">1,245</p>
                                <p class="text-xs text-green-500 mt-1 flex items-center">
                                    <i class="fas fa-arrow-up mr-1"></i> 8.3% from last month
                                </p>
                            </div>
                            <div class="bg-blue-100 p-3 rounded-lg">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 card-hover transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Active Projects</p>
                                <p class="text-2xl font-bold text-gray-800 mt-1">42</p>
                                <p class="text-xs text-red-500 mt-1 flex items-center">
                                    <i class="fas fa-arrow-down mr-1"></i> 2.4% from last month
                                </p>
                            </div>
                            <div class="bg-purple-100 p-3 rounded-lg">
                                <i class="fas fa-project-diagram text-purple-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6 card-hover transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-500">Avg. Session</p>
                                <p class="text-2xl font-bold text-gray-800 mt-1">4m 32s</p>
                                <p class="text-xs text-green-500 mt-1 flex items-center">
                                    <i class="fas fa-arrow-up mr-1"></i> 1.2% from last month
                                </p>
                            </div>
                            <div class="bg-pink-100 p-3 rounded-lg">
                                <i class="fas fa-clock text-pink-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                    <div class="bg-white rounded-xl shadow-sm p-6 lg:col-span-2">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-lg font-semibold text-gray-800">Revenue Overview</h2>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-xs bg-indigo-100 text-indigo-700 rounded-lg">Monthly</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-lg">Yearly</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <!-- Chart would be rendered here with a library like Chart.js -->
                            <div class="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
                                <p class="text-gray-500">Revenue chart would appear here</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h2 class="text-lg font-semibold text-gray-800 mb-6">Top Products</h2>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-mobile-alt text-indigo-600"></i>
                                </div>
                                <div class="ml-4 flex-1">
                                    <p class="text-sm font-medium text-gray-800">Mobile App</p>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                        <div class="bg-indigo-600 h-1.5 rounded-full" style="width: 72%"></div>
                                    </div>
                                </div>
                                <span class="text-sm font-medium text-gray-800">72%</span>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-laptop text-blue-600"></i>
                                </div>
                                <div class="ml-4 flex-1">
                                    <p class="text-sm font-medium text-gray-800">Desktop</p>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                        <div class="bg-blue-600 h-1.5 rounded-full" style="width: 56%"></div>
                                    </div>
                                </div>
                                <span class="text-sm font-medium text-gray-800">56%</span>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-tablet-alt text-purple-600"></i>
                                </div>
                                <div class="ml-4 flex-1">
                                    <p class="text-sm font-medium text-gray-800">Tablet</p>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                        <div class="bg-purple-600 h-1.5 rounded-full" style="width: 42%"></div>
                                    </div>
                                </div>
                                <span class="text-sm font-medium text-gray-800">42%</span>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-headphones text-pink-600"></i>
                                </div>
                                <div class="ml-4 flex-1">
                                    <p class="text-sm font-medium text-gray-800">Accessories</p>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                        <div class="bg-pink-600 h-1.5 rounded-full" style="width: 28%"></div>
                                    </div>
                                </div>
                                <span class="text-sm font-medium text-gray-800">28%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity & Tasks -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="bg-white rounded-xl shadow-sm p-6 lg:col-span-2">
                        <h2 class="text-lg font-semibold text-gray-800 mb-6">Recent Activity</h2>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-file-invoice-dollar text-indigo-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-800">New invoice #1250 created</p>
                                    <p class="text-xs text-gray-500 mt-1">Just now</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-user-plus text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-800">New user registered</p>
                                    <p class="text-xs text-gray-500 mt-1">15 minutes ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-800">High memory usage detected</p>
                                    <p class="text-xs text-gray-500 mt-1">1 hour ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-check-circle text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-800">Project "Nexus" completed</p>
                                    <p class="text-xs text-gray-500 mt-1">3 hours ago</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-comment-alt text-purple-600"></i>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-800">New comment on post</p>
                                    <p class="text-xs text-gray-500 mt-1">5 hours ago</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-lg font-semibold text-gray-800">Tasks</h2>
                            <button class="text-indigo-600 hover:text-indigo-800">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-800">Design new dashboard layout</p>
                                    <p class="text-xs text-gray-500 mt-1">Due tomorrow</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500" checked>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-800 line-through">Update documentation</p>
                                    <p class="text-xs text-gray-500 mt-1">Completed</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-800">Review pull requests</p>
                                    <p class="text-xs text-gray-500 mt-1">Due in 2 days</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-800">Prepare quarterly report</p>
                                    <p class="text-xs text-gray-500 mt-1">Due next week</p>
                                </div>
                            </div>
                            
                            <div class="flex items-start">
                                <input type="checkbox" class="mt-1 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-800">Schedule team meeting</p>
                                    <p class="text-xs text-gray-500 mt-1">No due date</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        // Simple interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle sidebar on mobile
            const sidebarToggle = document.querySelector('.lg\\:hidden');
            const sidebar = document.querySelector('.sidebar');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('hidden');
                });
            }
            
            // Card hover effect
            const cards = document.querySelectorAll('.card-hover');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transition = 'all 0.3s ease';
                });
            });
            
            // Active nav item indicator
            const navItems = document.querySelectorAll('nav ul li a');
            navItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    navItems.forEach(i => i.classList.remove('text-indigo-600', 'bg-indigo-50'));
                    this.classList.add('text-indigo-600', 'bg-indigo-50');
                });
            });
        });
    </script>
</body>
</html>