<?php
/**
 * author: unofficial
 * Date: 14/6/12
 * description: bycms qrcode
 */
defined('IN_PHPCMS') or exit('No permission resources.'); 
$value = $_GET["url"];
$ts    = $_GET['ts'];
$sk    = $_GET['sk'];

if($sk !== md5($ts . '0dV1a7Vi' . urlencode($value)))die();

include "phpqrcode.php";

$errorCorrectionLevel = "L";
$matrixPointSize = (int) $_GET["size"];
//路径
$fileName = preg_replace("/\W/","",$value);
$filePath = "statics/images/qr";
$path = "statics/images/qr/{$fileName}{$matrixPointSize}.png";
//生成后不再生成
function rePng($path) {
	Header("Content-type: image/png");
	$image = imagecreatefrompng($path);
    ImagePng($image);
}
//按路径生成
if(file_exists($path)) {
	rePng($path);
}else{
	mkdir($filePath);
	QRcode::png($value, $path, $errorCorrectionLevel, $matrixPointSize, 1);
    rePng($path);
}
?>
