<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频最后一帧提取器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .container {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-weight: 600;
        }
        
        .upload-section {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            border: 2px dashed #dcdfe6;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #409eff;
            background-color: rgba(64, 158, 255, 0.05);
        }
        
        .upload-section p {
            margin-bottom: 15px;
            color: #606266;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #409eff;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: 500;
        }
        
        .upload-btn:hover {
            background-color: #66b1ff;
        }
        
        .media-container {
            display: flex;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .video-container, .frame-container {
            flex: 1;
            min-width: 300px;
        }
        
        .container-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        video, canvas {
            width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background-color: #000;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        
        .btn {
            padding: 10px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #409eff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #66b1ff;
        }
        
        .btn-success {
            background-color: #67c23a;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #85ce61;
        }
        
        .btn:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
        
        .status {
            text-align: center;
            margin-top: 20px;
            color: #606266;
            font-size: 14px;
        }
        
        .hidden {
            display: none;
        }
        
        .file-info {
            margin-top: 10px;
            font-size: 14px;
            color: #606266;
        }
        
        .progress-container {
            position: relative;
            width: 100%;
            height: 6px;
            background-color: #e4e7ed;
            border-radius: 3px;
            margin-top: 15px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background-color: #409eff;
            width: 0;
            transition: width 0.3s;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .media-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>视频最后一帧提取器</h1>
        
        <div class="upload-section" id="dropArea">
            <p>选择或拖放视频文件到此处</p>
            <label for="videoInput" class="upload-btn">选择视频</label>
            <input type="file" id="videoInput" class="file-input" accept="video/*">
            <div class="file-info" id="fileInfo"></div>
            <div class="progress-container hidden" id="progressContainer">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>
        
        <div class="media-container">
            <div class="video-container">
                <h2 class="container-title">视频预览</h2>
                <video id="videoPreview" controls></video>
            </div>
            
            <div class="frame-container">
                <h2 class="container-title">最后一帧</h2>
                <canvas id="frameCanvas"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button id="extractBtn" class="btn btn-primary" disabled>提取最后一帧</button>
            <button id="downloadBtn" class="btn btn-success" disabled>下载图片</button>
        </div>
        
        <div class="status" id="statusMessage"></div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const videoInput = document.getElementById('videoInput');
            const videoPreview = document.getElementById('videoPreview');
            const frameCanvas = document.getElementById('frameCanvas');
            const extractBtn = document.getElementById('extractBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            const statusMessage = document.getElementById('statusMessage');
            const fileInfo = document.getElementById('fileInfo');
            const dropArea = document.getElementById('dropArea');
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            
            // 获取canvas上下文
            const ctx = frameCanvas.getContext('2d');
            
            // 视频元数据加载完成后的处理
            videoPreview.addEventListener('loadedmetadata', function() {
                // 设置canvas尺寸与视频相同
                frameCanvas.width = videoPreview.videoWidth;
                frameCanvas.height = videoPreview.videoHeight;
                
                // 启用提取按钮
                extractBtn.disabled = false;
                
                // 显示视频信息
                const duration = formatTime(videoPreview.duration);
                const resolution = `${videoPreview.videoWidth}x${videoPreview.videoHeight}`;
                fileInfo.innerHTML = `视频信息: ${resolution}, 时长: ${duration}`;
                
                statusMessage.textContent = '视频已加载，点击"提取最后一帧"按钮提取图像';
            });
            
            // 处理文件选择
            videoInput.addEventListener('change', function(e) {
                handleFileSelect(e.target.files);
            });
            
            // 拖放功能
            dropArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropArea.style.borderColor = '#409eff';
                dropArea.style.backgroundColor = 'rgba(64, 158, 255, 0.1)';
            });
            
            dropArea.addEventListener('dragleave', function() {
                dropArea.style.borderColor = '#dcdfe6';
                dropArea.style.backgroundColor = '';
            });
            
            dropArea.addEventListener('drop', function(e) {
                e.preventDefault();
                dropArea.style.borderColor = '#dcdfe6';
                dropArea.style.backgroundColor = '';
                
                if (e.dataTransfer.files.length) {
                    handleFileSelect(e.dataTransfer.files);
                }
            });
            
            // 提取最后一帧
            extractBtn.addEventListener('click', function() {
                extractLastFrame();
            });
            
            // 下载图片
            downloadBtn.addEventListener('click', function() {
                downloadFrame();
            });
            
            // 处理文件选择
            function handleFileSelect(files) {
                if (!files.length) return;
                
                const file = files[0];
                
                // 检查是否为视频文件
                if (!file.type.startsWith('video/')) {
                    statusMessage.textContent = '请选择有效的视频文件';
                    return;
                }
                
                // 显示文件名
                fileInfo.textContent = `已选择: ${file.name} (${formatFileSize(file.size)})`;
                
                // 显示进度条
                progressContainer.classList.remove('hidden');
                
                // 创建文件URL
                const videoURL = URL.createObjectURL(file);
                
                // 设置视频源
                videoPreview.src = videoURL;
                
                // 重置canvas和按钮状态
                ctx.clearRect(0, 0, frameCanvas.width, frameCanvas.height);
                downloadBtn.disabled = true;
                
                // 模拟加载进度
                simulateProgress();
            }
            
            // 提取最后一帧
            function extractLastFrame() {
                statusMessage.textContent = '正在提取最后一帧...';
                
                // 将视频跳转到最后
                videoPreview.currentTime = videoPreview.duration - 0.1;
                
                // 视频跳转后的处理
                videoPreview.addEventListener('seeked', function onSeeked() {
                    // 绘制当前帧到canvas
                    ctx.drawImage(videoPreview, 0, 0, frameCanvas.width, frameCanvas.height);
                    
                    // 启用下载按钮
                    downloadBtn.disabled = false;
                    
                    statusMessage.textContent = '最后一帧已提取，可以下载图片';
                    
                    // 移除事件监听器，防止多次触发
                    videoPreview.removeEventListener('seeked', onSeeked);
                });
            }
            
            // 下载图片
            function downloadFrame() {
                // 创建下载链接
                const link = document.createElement('a');
                
                // 获取文件名（不带扩展名）
                let fileName = 'video-last-frame';
                if (videoInput.files.length) {
                    fileName = videoInput.files[0].name.split('.').slice(0, -1).join('.');
                }
                
                // 设置下载属性
                link.download = `${fileName}-last-frame.png`;
                link.href = frameCanvas.toDataURL('image/png');
                
                // 触发下载
                link.click();
                
                statusMessage.textContent = '图片已下载';
            }
            
            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            // 格式化时间
            function formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                seconds = Math.floor(seconds % 60);
                
                return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
            }
            
            // 模拟加载进度
            function simulateProgress() {
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    if (progress > 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            progressContainer.classList.add('hidden');
                        }, 500);
                    } else {
                        progressBar.style.width = `${progress}%`;
                    }
                }, 100);
            }
        });
    </script>
</body>
</html>