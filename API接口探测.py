import requests
import json
import time 

def test_api():
    """基本的API测试程序"""
    
    # API端点URL
    base_url = "https://zj.v.api.aa1.cn/api/douyinjx/?text=https://v.douyin.com/rwdvOp7HMOI/"
    
    # 测试GET请求
    def test_get():
        endpoint = base_url
        try:
            response = requests.get(endpoint)
            print(f"GET请求状态码: {response.status_code}")
            
            # 尝试解析JSON响应
            try:
                json_response = response.json()
                print("JSON响应内容:")
                print(json.dumps(json_response, ensure_ascii=False, indent=2))
            except json.JSONDecodeError:
                print("响应不是有效的JSON格式")
                print(f"原始响应内容: {response.text}")
                
            # 检查响应状态码
            response.raise_for_status()
        except requests.RequestException as e:
            print(f"GET请求失败: {str(e)}")
    
    # 测试POST请求
    def test_post():
        endpoint = f"{base_url}/users"
        data = {
            "name": "测试用户",
            "email": "<EMAIL>"
        }
        response = requests.post(endpoint, json=data)
        print(f"POST请求状态码: {response.status_code}")
        print(f"响应内容: {response.json()}")

    # 执行测试
    try:
        print("开始API测试...")
        test_get()
        # 在GET和POST请求之间添加3秒延迟
        print("等待3秒后发起下一个请求...")
        time.sleep(3)
        test_post()
        print("API测试完成")
    except Exception as e:
        print(f"测试出错: {str(e)}")

if __name__ == "__main__":
    test_api()