<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>m3u8在线播放器与时间片段截取工具</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/hls.js@1.4.14/dist/hls.min.js"></script>
  
  <!-- Tailwind 配置 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#00B42A',
            danger: '#F53F3F',
            warning: '#FF7D00',
            dark: '#1D2129',
            light: '#F2F3F5',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      .player-shadow {
        box-shadow: 0 10px 25px -5px rgba(22, 93, 255, 0.1), 0 8px 10px -6px rgba(22, 93, 255, 0.1);
      }
      .time-marker {
        position: absolute;
        bottom: 0;
        width: 2px;
        height: 16px;
        background-color: #00B42A;
        transform: translateX(-50%);
        z-index: 10;
        transition: height 0.2s ease;
      }
      .time-marker:hover {
        height: 24px;
      }
      .clip-indicator {
        position: absolute;
        bottom: 0;
        height: 12px;
        background-color: rgba(0, 180, 42, 0.3);
        z-index: 5;
      }
      .btn-hover {
        @apply transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5;
      }
      .progress-thumb {
        @apply absolute w-4 h-4 rounded-full bg-primary -mt-1.5 cursor-pointer transition-all duration-150 hover:scale-125;
      }
      #clipHistory {
        max-height: 500px;  /* 设置最大高度 */
        overflow-y: auto;   /* 添加垂直滚动条 */
        overflow-x: hidden; /* 隐藏水平滚动条 */
        resize: vertical;   /* 允许垂直调整大小 */
      }
    }
  </style>
</head>
<body class="font-inter bg-gray-50 text-dark min-h-screen flex flex-col">
  <!-- 导航栏 -->
  <header class="bg-white shadow-sm sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-4 py-3 flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <i class="fa fa-play-circle text-primary text-2xl"></i>
        <h1 class="text-xl font-bold text-dark">M3U8<span class="text-primary">播放器</span></h1>
      </div>
      <div class="hidden md:flex items-center space-x-6">
        <a href="#" class="text-gray-600 hover:text-primary transition-colors">首页</a>
        <a href="#" class="text-gray-600 hover:text-primary transition-colors">使用指南</a>
        <a href="#" class="text-gray-600 hover:text-primary transition-colors">关于我们</a>
      </div>
      <div class="flex items-center space-x-3">
        <button class="hidden md:block px-4 py-2 rounded-md bg-primary text-white btn-hover">
          <i class="fa fa-github mr-1"></i> GitHub
        </button>
        <button class="md:hidden text-gray-600">
          <i class="fa fa-bars text-xl"></i>
        </button>
      </div>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <!-- 视频信息和控制区 -->
    <div class="mb-6 bg-white rounded-xl shadow-md p-4 md:p-6 transition-all duration-300 hover:shadow-lg">
      <div class="flex flex-col md:flex-row gap-6">
        <!-- 左侧视频播放区 -->
        <div class="md:w-2/3">
          <div class="relative rounded-lg overflow-hidden player-shadow bg-black aspect-video">
            <video id="videoPlayer" class="w-full h-full object-contain" controls></video>
            <!-- 加载中状态 -->
            <div id="loadingIndicator" class="absolute inset-0 flex items-center justify-center bg-black/70 z-20">
              <div class="flex flex-col items-center">
                <div class="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                <p class="mt-3 text-white">加载中...</p>
              </div>
            </div>
          </div>
          
          <!-- 时间片段预览区 -->
          <div class="mt-4 bg-gray-100 rounded-lg p-3 relative">
            <h3 class="text-sm font-medium text-gray-700 mb-2">时间片段预览</h3>
            <div class="relative h-16 bg-gray-200 rounded overflow-hidden">
              <div id="clipPreview" class="absolute inset-0 bg-cover bg-center opacity-70"></div>
              <div id="clipIndicator" class="clip-indicator"></div>
              <div id="startMarker" class="time-marker cursor-ew-resize" data-time="0"></div>
              <div id="endMarker" class="time-marker cursor-ew-resize" data-time="0"></div>
            </div>
            <div class="flex justify-between mt-2 text-xs text-gray-500">
              <span id="startTimeDisplay">00:00:00</span>
              <span id="endTimeDisplay">00:00:00</span>
              <span id="durationDisplay">00:00:00</span>
            </div>
          </div>
        </div>
        
        <!-- 右侧控制面板 -->
        <div class="md:w-1/3">
          <div class="space-y-5">
            <!-- M3U8 URL 输入 -->
            <div>
              <label for="m3u8Url" class="block text-sm font-medium text-gray-700 mb-1">M3U8 地址</label>
              <div class="relative">
                <input 
                  type="text" 
                  id="m3u8Url" 
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all"
                  placeholder="输入 m3u8 视频地址"
                  value="https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8"
                >
                <button id="loadVideoBtn" class="absolute right-2 top-1/2 -translate-y-1/2 bg-primary text-white px-3 py-1 rounded-md transition-colors duration-300 hover:bg-danger hover:text-gray-100">
                  <i class="fa fa-play mr-1"></i> 加载
                </button>
              </div>
            </div>
            
            <!-- 时间设置 -->
            <div>
              <h3 class="text-sm font-medium text-gray-700 mb-2">时间设置</h3>
              <div class="grid grid-cols-2 gap-3">
                <div>
                  <label for="startTime" class="block text-xs text-gray-500 mb-1">开始时间 (秒)</label>
                  <input 
                    type="number" 
                    id="startTime" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all text-sm"
                    min="0" 
                    value="0"
                  >
                </div>
                <div>
                  <label for="endTime" class="block text-xs text-gray-500 mb-1">结束时间 (秒)</label>
                  <input 
                    type="number" 
                    id="endTime" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all text-sm"
                    min="0" 
                    value="10"
                  >
                </div>
              </div>
              <div class="mt-3 flex justify-between">
                <button id="setStartTimeBtn" class="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-md text-sm transition-colors">
                  <i class="fa fa-clock-o mr-1"></i> 设置为当前时间
                </button>
                <button id="setEndTimeBtn" class="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-md text-sm transition-colors">
                  <i class="fa fa-clock-o mr-1"></i> 设置为当前时间
                </button>
              </div>
            </div>
            
            <!-- 控制按钮 -->
            <div class="flex flex-col sm:flex-row gap-3">
              <button id="playSelectedBtn" class="flex-1 bg-primary text-white px-4 py-2.5 rounded-lg btn-hover">
                <i class="fa fa-play-circle mr-1"></i> 播放选择片段
              </button>
              <button id="saveClipBtn" class="flex-1 bg-secondary text-white px-4 py-2.5 rounded-lg btn-hover">
                <i class="fa fa-download mr-1"></i> 保存片段
              </button>
            </div>
            
            <!-- 截取历史 -->
            <div>
              <div class="flex justify-between items-center mb-2">
                <h3 class="text-sm font-medium text-gray-700">截取历史</h3>
                <button id="clearHistoryBtn" class="text-xs text-gray-500 hover:text-danger transition-colors">
                  <i class="fa fa-trash-o"></i> 清空
                </button>
              </div>
              <div id="clipHistory" class="max-h-40 overflow-y-auto scrollbar-hide bg-gray-50 rounded-lg p-2 space-y-2">
                <!-- 历史记录将动态添加到这里 -->
                <div class="text-center text-gray-400 text-sm py-4">暂无截取记录</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 功能说明 -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
      <h2 class="text-lg font-bold text-gray-800 mb-4">功能说明</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-primary">
          <h3 class="font-medium text-primary mb-2 flex items-center">
            <i class="fa fa-play-circle mr-2"></i> 视频播放
          </h3>
          <p class="text-sm text-gray-600">支持播放来自网络的 m3u8 格式视频流，具有流畅的播放体验和标准的视频控制功能。</p>
        </div>
        <div class="bg-green-50 p-4 rounded-lg border-l-4 border-secondary">
          <h3 class="font-medium text-secondary mb-2 flex items-center">
            <i class="fa fa-scissors mr-2"></i> 片段截取
          </h3>
          <p class="text-sm text-gray-600">通过设置开始和结束时间，精确截取视频中的任意片段，支持拖拽时间标记进行可视化操作。</p>
        </div>
        <div class="bg-orange-50 p-4 rounded-lg border-l-4 border-warning">
          <h3 class="font-medium text-warning mb-2 flex items-center">
            <i class="fa fa-download mr-2"></i> 片段保存
          </h3>
          <p class="text-sm text-gray-600">将选定的视频片段保存为新的 m3u8 文件，支持下载和分享截取的内容。</p>
        </div>
        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-500 col-span-1 md:col-span-3">
          <h3 class="font-medium text-purple-500 mb-2 flex items-center">
            <i class="fa fa-lightbulb mr-2"></i> 智能推算功能
          </h3>
          <p class="text-sm text-gray-600">针对没有时间信息的m3u8文件，系统会自动通过视频总时长和ts文件总数量来智能推算每个片段的时间点，确保即使在缺少时间信息的情况下也能准确截取视频片段。</p>
        </div>
      </div>
    </div>
  </main>

  <!-- 页脚 -->
  <footer class="bg-dark text-white py-8">
    <div class="container mx-auto px-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div>
          <h3 class="text-lg font-bold mb-4 flex items-center">
            <i class="fa fa-play-circle text-primary mr-2"></i> M3U8播放器
          </h3>
          <p class="text-gray-400 text-sm">专业的在线 m3u8 视频播放器，支持视频片段截取和保存功能，为您提供便捷的视频处理体验。</p>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">快速链接</h3>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-primary transition-colors">首页</a></li>
            <li><a href="#" class="hover:text-primary transition-colors">使用指南</a></li>
            <li><a href="#" class="hover:text-primary transition-colors">常见问题</a></li>
            <li><a href="#" class="hover:text-primary transition-colors">关于我们</a></li>
          </ul>
        </div>
        <div>
          <h3 class="text-lg font-bold mb-4">联系我们</h3>
          <ul class="space-y-2 text-gray-400">
            <li class="flex items-center"><i class="fa fa-envelope-o mr-2"></i> <EMAIL></li>
            <li class="flex items-center"><i class="fa fa-github mr-2"></i> GitHub</li>
            <li class="flex items-center"><i class="fa fa-twitter mr-2"></i> Twitter</li>
          </ul>
        </div>
      </div>
      <div class="mt-8 pt-6 border-t border-gray-700 text-center text-gray-500 text-sm">
        &copy; 2025 M3U8播放器 版权所有
      </div>
    </div>
  </footer>

  <!-- Toast提示 -->
  <div id="successToast" class="fixed bottom-4 right-4 bg-secondary text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-500 flex items-center z-50">
    <i class="fa fa-check-circle mr-2"></i>
    <span>片段信息已保存！</span>
  </div>

  <!-- 复制成功提示 -->
  <div id="copyToast" class="fixed bottom-4 right-4 bg-primary/90 text-white px-4 py-2 rounded-lg shadow-lg transform -translate-y-20 opacity-0 transition-all duration-300 flex items-center z-50">
    <i class="fa fa-check mr-2"></i>
    <span>链接已复制到剪贴板</span>
  </div>

  <script>
    // 全局变量
    let hls = null;
    let videoDuration = 0;
    let clipHistory = [];
    
    // DOM 元素
    const videoPlayer = document.getElementById('videoPlayer');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const m3u8Url = document.getElementById('m3u8Url');
    const loadVideoBtn = document.getElementById('loadVideoBtn');
    const startTime = document.getElementById('startTime');
    const endTime = document.getElementById('endTime');
    const setStartTimeBtn = document.getElementById('setStartTimeBtn');
    const setEndTimeBtn = document.getElementById('setEndTimeBtn');
    const playSelectedBtn = document.getElementById('playSelectedBtn');
    const saveClipBtn = document.getElementById('saveClipBtn');
    const clipHistoryElement = document.getElementById('clipHistory');
    const clearHistoryBtn = document.getElementById('clearHistoryBtn');
    const startTimeDisplay = document.getElementById('startTimeDisplay');
    const endTimeDisplay = document.getElementById('endTimeDisplay');
    const durationDisplay = document.getElementById('durationDisplay');
    const startMarker = document.getElementById('startMarker');
    const endMarker = document.getElementById('endMarker');
    const clipIndicator = document.getElementById('clipIndicator');
    const clipPreview = document.getElementById('clipPreview');
    const successToast = document.getElementById('successToast');
    
    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
      // 加载示例视频
      loadVideo(m3u8Url.value);
      
      // 事件监听
      loadVideoBtn.addEventListener('click', () => loadVideo(m3u8Url.value));
      setStartTimeBtn.addEventListener('click', () => setCurrentTimeAsStartTime());
      setEndTimeBtn.addEventListener('click', () => setCurrentTimeAsEndTime());
      playSelectedBtn.addEventListener('click', playSelectedClip);
      saveClipBtn.addEventListener('click', saveClip);
      clearHistoryBtn.addEventListener('click', clearClipHistory);
      
      // 时间输入框变化事件
      startTime.addEventListener('input', updateTimeMarkers);
      endTime.addEventListener('input', updateTimeMarkers);
      
      // 拖拽时间标记
      setupDragHandlers(startMarker, (pos) => {
        const time = Math.min(pos * videoDuration, parseFloat(endTime.value) - 0.5);
        startTime.value = Math.max(0, time).toFixed(2);
        updateTimeMarkers();
      });
      
      setupDragHandlers(endMarker, (pos) => {
        const time = Math.max(pos * videoDuration, parseFloat(startTime.value) + 0.5);
        endTime.value = Math.min(videoDuration, time).toFixed(2);
        updateTimeMarkers();
      });
      
      // 视频元数据加载完成
      videoPlayer.addEventListener('loadedmetadata', () => {
        videoDuration = videoPlayer.duration;
        endTime.value = Math.min(10, videoDuration).toFixed(2);
        updateTimeMarkers();
        loadingIndicator.classList.add('opacity-0');
        setTimeout(() => loadingIndicator.classList.add('hidden'), 300);
      });
      
      // 视频进度更新
      videoPlayer.addEventListener('timeupdate', updateProgress);
      
      // 历史记录点击事件委托
      clipHistory.addEventListener('click', (e) => {
        if (e.target.closest('.delete-history')) {
          const index = parseInt(e.target.closest('.delete-history').dataset.index);
          deleteHistoryClip(index);
        }
      });
    });
    
    // 加载视频
    function loadVideo(url) {
      if (!url) {
        alert('请输入有效的 m3u8 地址');
        return;
      }
      
      loadingIndicator.classList.remove('hidden', 'opacity-0');
      
      // 如果 HLS 实例已存在，则销毁它
      if (hls) {
        hls.destroy();
        hls = null;
      }
      
      // 检查浏览器是否支持 HLS
      if (Hls.isSupported()) {
        hls = new Hls();
        hls.loadSource(url);
        hls.attachMedia(videoPlayer);
        
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          videoPlayer.play();
        });
        
        hls.on(Hls.Events.ERROR, (event, data) => {
          console.error('HLS 错误:', data);
          if (data.fatal) {
            switch(data.type) {
              case Hls.ErrorTypes.NETWORK_ERROR:
                console.log('网络错误，尝试重新连接...');
                hls.startLoad();
                break;
              case Hls.ErrorTypes.MEDIA_ERROR:
                console.log('媒体错误，尝试修复...');
                hls.recoverMediaError();
                break;
              default:
                console.log('无法恢复的错误，停止加载');
                hls.destroy();
                hls = null;
                loadingIndicator.classList.add('opacity-0');
                setTimeout(() => loadingIndicator.classList.add('hidden'), 300);
                alert('加载视频失败，请检查 URL 是否有效');
                break;
            }
          }
        });
      } 
      // 回退到原生 HLS 支持
      else if (videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
        videoPlayer.src = url;
        videoPlayer.addEventListener('loadedmetadata', () => {
          videoPlayer.play();
        });
      } else {
        loadingIndicator.classList.add('opacity-0');
        setTimeout(() => loadingIndicator.classList.add('hidden'), 300);
        alert('您的浏览器不支持 HLS 视频播放');
      }
    }
    
    // 设置当前时间为开始时间
    function setCurrentTimeAsStartTime() {
      startTime.value = videoPlayer.currentTime.toFixed(2);
      updateTimeMarkers();
    }
    
    // 设置当前时间为结束时间
    function setCurrentTimeAsEndTime() {
      endTime.value = videoPlayer.currentTime.toFixed(2);
      updateTimeMarkers();
    }
    
    // 播放选择的片段
    function playSelectedClip() {
      if (!videoDuration) return;
      
      const start = parseFloat(startTime.value);
      const end = parseFloat(endTime.value);
      
      if (start >= end) {
        alert('开始时间必须小于结束时间');
        return;
      }
      
      // 如果视频已暂停，从开始时间播放
      if (videoPlayer.paused) {
        videoPlayer.currentTime = start;
        videoPlayer.play();
      }
      
      // 设置监听器，当播放到结束时间时暂停
      const checkEndTime = () => {
        if (videoPlayer.currentTime >= end) {
          videoPlayer.pause();
          videoPlayer.removeEventListener('timeupdate', checkEndTime);
        }
      };
      
      videoPlayer.addEventListener('timeupdate', checkEndTime);
    }
    
    // 保存片段
    function saveClip() {
      if (!videoDuration) return;
      
      const start = parseFloat(startTime.value);
      const end = parseFloat(endTime.value);
      
      if (start >= end) {
        alert('开始时间必须小于结束时间');
        return;
      }

      if (!hls) {
        alert('请先加载视频');
        return;
      }

      // 获取当前播放的m3u8清单
      const playlist = hls.levels[hls.currentLevel];
      if (!playlist) {
        alert('无法获取视频片段信息');
        return;
      }

      let tsUrls = [];
      const fragments = playlist.details.fragments;
      
      // 检查是否有时间信息
      const hasTimeInfo = fragments.length > 0 && fragments[0].start !== undefined;
      
      if (hasTimeInfo) {
        // 方法1：通过时间信息获取片段索引
        const startIndex = fragments.findIndex(f => f.start >= start);
        const endIndex = fragments.findIndex(f => f.start > end);
        
        // 提取时间范围内的ts文件URL
        tsUrls = fragments
          .slice(startIndex, endIndex === -1 ? undefined : endIndex)
          .map(f => f.url);
      } else {
        // 方法2：通过视频总时长和ts文件总数量来推算
        console.log("m3u8文件中没有时间信息，使用推算方法");
        
        // 计算每个片段的平均时长
        const avgDuration = videoDuration / fragments.length;
        
        // 计算开始和结束片段索引
        const startIndex = Math.floor(start / avgDuration);
        const endIndex = Math.ceil(end / avgDuration);
        
        // 提取时间范围内的ts文件URL
        tsUrls = fragments
          .slice(
            Math.max(0, startIndex), 
            Math.min(fragments.length, endIndex)
          )
          .map(f => f.url);
          
        console.log(`视频总时长: ${videoDuration}秒, TS文件总数: ${fragments.length}`);
        console.log(`每个片段平均时长: ${avgDuration.toFixed(2)}秒`);
        console.log(`选择的时间范围: ${start.toFixed(2)}秒 - ${end.toFixed(2)}秒`);
        console.log(`对应的片段索引: ${startIndex} - ${endIndex}`);
      }

      // 创建一个新的片段对象并添加到历史记录
      const clip = {
        id: Date.now(),
        startTime: start,
        endTime: end,
        duration: end - start,
        createdAt: new Date().toLocaleString(),
        tsUrls: tsUrls
      };
      
      clipHistory.unshift(clip);
      updateClipHistoryUI();
      
      // 显示成功提示
      showSuccessToast();
    }
    
    // 播放历史记录中的片段
    function playHistoryClip(index) {
      const clip = clipHistory[index];
      if (clip) {
        startTime.value = clip.startTime.toFixed(2);
        endTime.value = clip.endTime.toFixed(2);
        updateTimeMarkers();
        playSelectedClip();
      }
    }
    
    // 删除历史记录中的片段
    function deleteHistoryClip(index) {
      clipHistory.splice(index, 1);
      updateClipHistoryUI();
    }
    
    // 清空历史记录
    function clearClipHistory() {
      clipHistory = [];
      updateClipHistoryUI();
    }
    
    // 更新时间标记
    function updateTimeMarkers() {
      if (!videoDuration) return;
      
      const start = Math.max(0, Math.min(parseFloat(startTime.value), videoDuration - 0.1));
      const end = Math.min(videoDuration, Math.max(parseFloat(endTime.value), start + 0.1));
      
      // 更新输入框值
      startTime.value = start.toFixed(2);
      endTime.value = end.toFixed(2);
      
      // 更新时间标记位置
      const startPercent = (start / videoDuration) * 100;
      const endPercent = (end / videoDuration) * 100;
      
      startMarker.style.left = `${startPercent}%`;
      endMarker.style.left = `${endPercent}%`;
      startMarker.dataset.time = formatTime(start);
      endMarker.dataset.time = formatTime(end);
      
      // 更新片段指示器
      clipIndicator.style.left = `${startPercent}%`;
      clipIndicator.style.width = `${endPercent - startPercent}%`;
      
      // 更新时间显示
      startTimeDisplay.textContent = formatTime(start);
      endTimeDisplay.textContent = formatTime(end);
      durationDisplay.textContent = formatTime(end - start);
      
      // 更新预览图
      updateClipPreview(start, end);
    }
    
    // 更新视频进度
    function updateProgress() {
      if (!videoDuration) return;
      
      const progressPercent = (videoPlayer.currentTime / videoDuration) * 100;
      
      // 这里可以更新进度条样式
    }
    
    // 更新片段预览
    function updateClipPreview(start, end) {
      // 实际应用中可以从视频中提取关键帧作为预览
      // 这里使用示例图片模拟
      const randomId = Math.floor(Math.random() * 1000);
      clipPreview.style.backgroundImage = `url(https://picsum.photos/seed/${randomId}/800/200)`;
    }
    
    // 更新历史记录 UI
    function updateClipHistoryUI() {
      clipHistoryElement.innerHTML = '';
      
      if (clipHistory.length === 0) {
        clipHistoryElement.innerHTML = '<div class="text-center text-gray-400 text-sm py-4">暂无截取记录</div>';
        return;
      }
      
      clipHistory.forEach((clip, index) => {
        const item = document.createElement('div');
        item.className = 'bg-white p-3 rounded-md shadow-sm hover:shadow transition-shadow mb-2';
        
        // 创建片段头部信息
        const header = document.createElement('div');
        header.className = 'flex justify-between items-center mb-2';
        header.innerHTML = `
          <div>
            <div class="text-sm font-medium">片段 ${index + 1}</div>
            <div class="text-xs text-gray-500">
              ${formatTime(clip.startTime)} - ${formatTime(clip.endTime)} (${formatTime(clip.duration)})
            </div>
            <div class="text-xs text-gray-400">${clip.createdAt}</div>
          </div>
          <div class="flex space-x-2">
            <button class="copy-all-urls text-gray-400 hover:text-primary transition-colors" data-index="${index}" title="复制所有链接">
              <i class="fa fa-copy"></i>
            </button>
            <button class="delete-history text-gray-400 hover:text-danger transition-colors" data-index="${index}">
              <i class="fa fa-trash-o"></i>
            </button>
            <button class="toggle-ts-list text-gray-400 hover:text-primary transition-colors" data-index="${index}">
              <i class="fa fa-list"></i>
            </button>
          </div>
        `;
        
        // 创建ts文件列表容器
        const tsContainer = document.createElement('div');
        tsContainer.className = 'ts-list hidden mt-2';
        
        // 添加ts文件列表信息头部
        const tsHeader = document.createElement('div');
        tsHeader.className = 'flex justify-between items-center mb-2 px-2';
        tsHeader.innerHTML = `
          <div class="flex justify-between items-center text-xs text-gray-500">
            <span>共 ${clip.tsUrls ? clip.tsUrls.length : 0} 个片段，</span>
            <span>点击下载单个或复制全部</span>
          </div>
        `;
        
        // 创建ts文件列表
        const tsList = document.createElement('div');
        tsList.className = 'space-y-1 bg-gray-50 p-2 rounded max-h-40 overflow-y-auto';
        
        if (clip.tsUrls && clip.tsUrls.length > 0) {
          clip.tsUrls.forEach((url, tsIndex) => {
            const tsItem = document.createElement('div');
            tsItem.className = 'text-xs flex items-center hover:bg-gray-100 p-1 rounded';
            tsItem.innerHTML = `
              <span class="w-16 text-gray-400">片段 ${tsIndex + 1}</span>
              <a href="${url}" target="_blank" class="text-primary hover:text-primary/80 break-all flex-1">
                <i class="fa fa-download mr-1"></i>${url.split('/').pop()}
              </a>
            `;
            tsList.appendChild(tsItem);
          });
        } else {
          tsList.innerHTML = '<div class="text-xs text-gray-400 text-center py-2">无可用的视频片段</div>';
        }
        
        tsContainer.appendChild(tsHeader);
        tsContainer.appendChild(tsList);
        item.appendChild(header);
        item.appendChild(tsContainer);
        clipHistoryElement.appendChild(item);
        
        // 添加展开/收起ts列表的点击事件
        const toggleButton = item.querySelector('.toggle-ts-list');
        const tsListElement = item.querySelector('.ts-list');
        
        toggleButton.addEventListener('click', () => {
          tsListElement.classList.toggle('hidden');
          const icon = toggleButton.querySelector('i');
          icon.classList.toggle('fa-list');
          icon.classList.toggle('fa-chevron-up');
        });
        
        // 添加复制所有链接的点击事件
        const copyButton = item.querySelector('.copy-all-urls');
        copyButton.addEventListener('click', () => {
          if (!clip.tsUrls || clip.tsUrls.length === 0) {
            alert('没有可用的链接');
            return;
          }
          
          const urlsText = clip.tsUrls.join('\n');
          navigator.clipboard.writeText(urlsText).then(() => {
            showCopyToast();
            copyButton.style.color = '#00B42A';
            setTimeout(() => {
              copyButton.style.color = '';
            }, 2000);
          }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请手动复制链接');
          });
        });
      });
    }
    
    // 显示成功提示
    function showSuccessToast() {
      successToast.classList.remove('translate-y-20', 'opacity-0');
      
      setTimeout(() => {
        successToast.classList.add('translate-y-20', 'opacity-0');
      }, 3000);
    }
    
    // 显示复制成功提示
    function showCopyToast() {
      const copyToast = document.getElementById('copyToast');
      copyToast.classList.remove('-translate-y-20', 'opacity-0');
      
      setTimeout(() => {
        copyToast.classList.add('-translate-y-20', 'opacity-0');
      }, 2000);
    }
    
    // 格式化时间
    function formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const mins = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);
      const ms = Math.floor((seconds - Math.floor(seconds)) * 100);
      
      return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`;
    }
    
    // 设置拖拽处理
    function setupDragHandlers(element, onDrag) {
      let isDragging = false;
      
      element.addEventListener('mousedown', (e) => {
        isDragging = true;
        document.body.style.userSelect = 'none';
        element.classList.add('scale-125');
        e.preventDefault();
      });
      
      document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        
        const rect = clipIndicator.parentElement.getBoundingClientRect();
        const pos = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
        
        onDrag(pos);
      });
      
      document.addEventListener('mouseup', () => {
        if (isDragging) {
          isDragging = false;
          document.body.style.userSelect = '';
          element.classList.remove('scale-125');
        }
      });
      
      // 触摸设备支持
      element.addEventListener('touchstart', (e) => {
        isDragging = true;
        document.body.style.userSelect = 'none';
        element.classList.add('scale-125');
        e.preventDefault();
      });
      
      document.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        
        const rect = clipIndicator.parentElement.getBoundingClientRect();
        const pos = Math.max(0, Math.min(1, (e.touches[0].clientX - rect.left) / rect.width));
        
        onDrag(pos);
      });
      
      document.addEventListener('touchend', () => {
        if (isDragging) {
          isDragging = false;
          document.body.style.userSelect = '';
          element.classList.remove('scale-125');
        }
      });
    }
  </script>
</body>
</html>