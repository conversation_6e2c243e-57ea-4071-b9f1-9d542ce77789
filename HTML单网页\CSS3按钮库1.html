<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS 按钮展示库 - 深色背景</title>
    <style>
        /* 全局样式和深色背景 */
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #1a1a2e; /* 深色背景 */
            color: #e0e0e0; /* 浅色文字 */
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            box-sizing: border-box;
        }

        h1 {
            color: #e94560;
            margin-bottom: 40px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .button-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            max-width: 1200px;
            width: 100%;
            padding: 20px;
            background-color: #16213e; /* 容器背景稍亮 */
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
        }

        /* 基础按钮样式 */
        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            outline: none;
            transition: all 0.3s ease; /* 平滑过渡 */
            text-align: center;
            text-decoration: none; /* 确保作为链接时没有下划线 */
            display: inline-block; /* 确保按钮可以居中或与其他元素对齐 */
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            color: #fff; /* 默认文字颜色 */
        }

        /* 悬停和点击通用效果 */
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.4);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        }

        /* 1. 主题色按钮 */
        .btn-primary {
            background-color: #0f3460;
            color: #e0e0e0;
        }
        .btn-primary:hover {
            background-color: #1b549f;
        }

        /* 2. 成功按钮 */
        .btn-success {
            background-color: #4CAF50;
        }
        .btn-success:hover {
            background-color: #45a049;
        }

        /* 3. 警告按钮 */
        .btn-warning {
            background-color: #ff9800;
        }
        .btn-warning:hover {
            background-color: #e68a00;
        }

        /* 4. 危险按钮 */
        .btn-danger {
            background-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #da190b;
        }

        /* 5. 信息按钮 */
        .btn-info {
            background-color: #2196F3;
        }
        .btn-info:hover {
            background-color: #0b7dda;
        }

        /* 6. 渐变按钮 */
        .btn-gradient {
            background-image: linear-gradient(to right, #e94560 0%, #ff7e5f 50%, #e94560 100%);
            background-size: 200% auto;
            color: white;
            box-shadow: 0 5px 15px rgba(233, 69, 96, 0.4);
        }
        .btn-gradient:hover {
            background-position: right center; /* 移动渐变背景 */
            box-shadow: 0 8px 20px rgba(233, 69, 96, 0.6);
        }

        /* 7. 描边按钮 */
        .btn-outline {
            background-color: transparent;
            border: 2px solid #e94560;
            color: #e94560;
            box-shadow: none;
        }
        .btn-outline:hover {
            background-color: #e94560;
            color: white;
            box-shadow: 0 4px 10px rgba(233, 69, 96, 0.4);
        }
        .btn-outline:active {
            background-color: #c7374f;
            color: white;
        }

        /* 8. 圆角按钮 (Pill Button) */
        .btn-pill {
            border-radius: 50px;
            background-color: #6a0572;
        }
        .btn-pill:hover {
            background-color: #8e2de2;
        }

        /* 9. 幽灵按钮 (Ghost Button) */
        .btn-ghost {
            background-color: transparent;
            border: 2px solid #0f3460;
            color: #0f3460;
            box-shadow: none;
        }
        .btn-ghost:hover {
            background-color: #0f3460;
            color: #e0e0e0;
            box-shadow: 0 4px 10px rgba(15, 52, 96, 0.4);
        }
        .btn-ghost:active {
            background-color: #0a2340;
            color: #e0e0e0;
        }

        /* 10. 带图标按钮 */
        .btn-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #3f72af;
        }
        .btn-icon i {
            margin-right: 8px;
            font-size: 1.2em;
        }
        .btn-icon:hover {
            background-color: #112d4e;
        }

        /* 11. 3D 效果按钮 */
        .btn-3d {
            background-color: #e94560;
            position: relative;
            box-shadow: 0 6px #c7374f; /* 底部阴影模拟3D效果 */
            transition: all 0.1s ease;
        }
        .btn-3d:hover {
            transform: translateY(-3px);
            box-shadow: 0 9px #c7374f;
        }
        .btn-3d:active {
            transform: translateY(3px);
            box-shadow: 0 0 #c7374f; /* 点击时阴影消失，模拟按下 */
        }

        /* 12. 闪光效果按钮 (Shine Effect) */
        .btn-shine {
            position: relative;
            overflow: hidden;
            background-color: #00bcd4;
            z-index: 1;
        }
        .btn-shine::before {
            content: '';
            position: absolute;
            top: 0;
            left: -75%;
            width: 50%;
            height: 100%;
            background: rgba(255, 255, 255, 0.3);
            transform: skewX(-20deg);
            transition: all 0.5s ease;
            z-index: -1;
        }
        .btn-shine:hover::before {
            left: 125%;
        }

        /* 引入 Font Awesome 图标库 (用于带图标按钮) */
        @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css' );

    </style>
</head>
<body>
    <h1>CSS 按钮展示库</h1>

    <div class="button-container">
        <button class="btn btn-primary">主要按钮</button>
        <button class="btn btn-success">成功按钮</button>
        <button class="btn btn-warning">警告按钮</button>
        <button class="btn btn-danger">危险按钮</button>
        <button class="btn btn-info">信息按钮</button>
        <button class="btn btn-gradient">渐变按钮</button>
        <button class="btn btn-outline">描边按钮</button>
        <button class="btn btn-pill">圆角按钮</button>
        <button class="btn btn-ghost">幽灵按钮</button>
        <button class="btn btn-icon"><i class="fas fa-download"></i> 下载</button>
        <button class="btn btn-3d">3D 效果</button>
        <button class="btn btn-shine">闪光效果</button>
    </div>
</body>
</html>
