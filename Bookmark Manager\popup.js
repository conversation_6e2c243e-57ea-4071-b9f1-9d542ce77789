document.addEventListener('DOMContentLoaded', () => {
  const bookmarksListDiv = document.getElementById('bookmarksList');
  const searchInput = document.getElementById('searchInput');
  const sortOrderSelect = document.getElementById('sortOrder');
  const resetAllButton = document.getElementById('resetAllButton');
  const bookmarkCountSpan = document.getElementById('bookmarkCount');
  const clearSearchInput = document.getElementById('clearSearchInput'); // 新增
  const editModeCheckbox = document.getElementById('editMode'); // 新增编辑模式勾选框

  let allBookmarksData = [];
  let isEditMode = false; // 默认不是编辑模式
  
  // 防抖函数，用于搜索输入
  function debounce(func, wait) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        func.apply(context, args);
      }, wait);
    };
  }

  async function loadAndDisplayBookmarks() {
    try {
      bookmarksListDiv.innerHTML = '<p>正在加载书签数据...</p>';
      if (bookmarkCountSpan) {
        bookmarkCountSpan.textContent = '(加载中...)';
      }
      const bookmarkTreeNodes = await chrome.bookmarks.getTree();
      const stats = await chrome.storage.local.get(null);
      
      const settings = await chrome.storage.local.get('settings');

      allBookmarksData = [];
      parseBookmarkNodes(bookmarkTreeNodes, stats);
      
      renderBookmarks(); // 初始渲染
    } catch (e) {
      console.error("加载或显示书签时出错:", e);
      bookmarksListDiv.innerHTML = '<p>加载书签数据失败。</p>';
      if (bookmarkCountSpan) {
        bookmarkCountSpan.textContent = '(获取失败)';
      }
    }
  }

  function parseBookmarkNodes(nodes, stats, path = []) {
    for (const node of nodes) {
      if (node.url) {
        const bookmarkStat = stats[node.url] || { count: 0, lastClicked: null };
        allBookmarksData.push({
          id: node.id,
          title: node.title || node.url,
          url: node.url,
          count: bookmarkStat.count,
          lastClicked: bookmarkStat.lastClicked ? new Date(bookmarkStat.lastClicked) : null,
          folderPath: [...path]
        });
      }
      if (node.children) {
        const newPath = [...path, node.title || '未命名文件夹']; // 确保文件夹名称存在
        parseBookmarkNodes(node.children, stats, newPath);
      }
    }
  }

  function renderBookmarks() {
    bookmarksListDiv.innerHTML = '';

    if (bookmarkCountSpan) {
      bookmarkCountSpan.textContent = `(🏷️: ${allBookmarksData.length} )`;
    }

    let dataToRender = [...allBookmarksData];

    // 筛选
    const searchTerm = searchInput.value.toLowerCase();
    if (searchTerm) {
      dataToRender = dataToRender.filter(item => 
        item.title.toLowerCase().includes(searchTerm) || 
        item.url.toLowerCase().includes(searchTerm)
      );
    }

    // 排序
    const sortValue = sortOrderSelect.value;
    dataToRender.sort((a, b) => {
      switch (sortValue) {
        case 'title_asc':
          return a.title.localeCompare(b.title);
        case 'title_desc':
          return b.title.localeCompare(a.title);
        case 'count_desc':
          return (b.count || 0) - (a.count || 0);
        case 'count_asc':
          return (a.count || 0) - (b.count || 0);
        case 'lastClicked_desc':
          return (b.lastClicked || 0) - (a.lastClicked || 0);
        case 'lastClicked_asc':
          return (a.lastClicked || 0) - (b.lastClicked || 0);
        default:
          return 0;
      }
    });

    if (dataToRender.length === 0) {
      bookmarksListDiv.innerHTML = '<p>没有找到匹配的书签，或者还没有书签被追踪。</p>';
      // 即便没有匹配的，总数依然显示 allBookmarksData.length
      return;
    }

    dataToRender.forEach(item => {
      const div = document.createElement('div');
      div.className = 'bookmark-item';
      div.innerHTML = `
        <h3 class="bookmark-title">
            <span class="click-count">${item.count}</span> 
            <a href="${item.url}" target="_blank" data-url="${item.url}" class="bookmark-link">${item.title}</a>
        </h3>
        <div class="actions">
            <button class="reset-single" data-url="${item.url}" data-title="${item.title}" title="重置此书签统计">
                <i class="fas fa-history"></i>
            </button>
            <button class="delete-bookmark" data-id="${item.id}" data-title="${item.title}" title="删除书签">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="bookmark-details" style="display: none;">
            <p class="url">${item.url}</p>
            ${item.folderPath.length > 0 ? `<p>文件夹: ${item.folderPath.slice(1).join(' / ')}</p>` : ''}
            <p>上次点击: ${item.lastClicked ? item.lastClicked.toLocaleString() : '从未'}</p>
        </div>
        `;
      bookmarksListDiv.appendChild(div);
    });

    document.querySelectorAll('.bookmark-item').forEach(itemDiv => {

      itemDiv.addEventListener('click', (event) => {
        // 如果点击的是链接 <a> 或按钮，则不触发折叠
        if (event.target.closest('a') || event.target.closest('button')) {
          return;
        }
        
        // 初始化时修改标题样式
        const h3 = itemDiv.querySelector('h3');
        const details = itemDiv.querySelector('.bookmark-details');
        if (h3) {
          if (details.style.display === 'none') {
            h3.style.whiteSpace = 'normal';
            h3.style.overflow = 'visible';
          } else {
            h3.style.whiteSpace = 'nowrap';
            h3.style.overflow = 'hidden';
          }
        }
        if (details.style.display === 'none') {
          details.style.display = 'block';
        } else {
          details.style.display = 'none';
        }
      });
    });

    // 为单个重置按钮添加事件监听器
    document.querySelectorAll('.reset-single').forEach(button => {
      button.addEventListener('click', async (event) => {
        const urlToReset = event.currentTarget.dataset.url;
        const titleToReset = event.currentTarget.dataset.title;
        if (confirm(`确定要重置书签 "${titleToReset}" 的点击统计吗？`)) {
          try {
            await chrome.storage.local.set({ [urlToReset]: { count: 0, lastClicked: null } });
            console.log("已重置书签统计:", titleToReset);
            if (!isEditMode) { // 只有在非编辑模式下才重新加载数据
              loadAndDisplayBookmarks(); // 重新加载数据
            }
          } catch (e) {
            console.error("重置单个书签统计时出错:", e);
          }
        }
      });
    });

    // 为删除书签按钮添加事件监听器
    document.querySelectorAll('.delete-bookmark').forEach(button => {
      button.addEventListener('click', async (event) => {
        const bookmarkIdToDelete = event.currentTarget.dataset.id;
        const bookmarkTitle = event.currentTarget.dataset.title;
        if (confirm(`确定要删除书签 "${bookmarkTitle}" 吗？此操作不可撤销。`)) {
          try {
            await chrome.bookmarks.remove(bookmarkIdToDelete);
            console.log("已删除书签:", bookmarkTitle, bookmarkIdToDelete);
            // 从 allBookmarksData 中移除已删除的书签，避免在下次渲染前仍然显示
            allBookmarksData = allBookmarksData.filter(b => b.id !== bookmarkIdToDelete);
            
            if (!isEditMode) { // 只有在非编辑模式下才重新加载数据
              loadAndDisplayBookmarks(); // 重新加载并渲染书签列表
            } else {
              // 在编辑模式下，只重新渲染当前数据，不重新加载
              renderBookmarks();
            }
          } catch (e) {
            console.error("删除书签时出错:", e);
            // 根据错误类型可以给用户更友好的提示
            if (e.message.includes("Can't find bookmark for id")) {
              alert(`无法找到ID为 ${bookmarkIdToDelete} 的书签，可能已被删除。将刷新列表。`);
              loadAndDisplayBookmarks();
            } else {
              alert(`删除书签 "${bookmarkTitle}" 失败。`);
            }
          }
        }
      });
    });
  }

  // 事件监听器
  // 修改：对搜索输入应用防抖
  const debouncedRenderBookmarks = debounce(renderBookmarks, 300); // 延迟300毫秒
  searchInput.addEventListener('input', function() {
    if (clearSearchInput) {
      clearSearchInput.style.display = searchInput.value ? 'inline-block' : 'none';
    }
    debouncedRenderBookmarks();
  });

  if (clearSearchInput) {
    clearSearchInput.addEventListener('click', function() {
      searchInput.value = '';
      clearSearchInput.style.display = 'none';
      searchInput.dispatchEvent(new Event('input')); // 触发搜索刷新
    });
    // 初始化时隐藏
    clearSearchInput.style.display = 'none';
  }

  sortOrderSelect.addEventListener('change', renderBookmarks);

  resetAllButton.addEventListener('click', async () => {
    if (confirm("确定要重置所有书签的点击统计吗？此操作不可撤销。")) {
      try {
        const allData = await chrome.storage.local.get(null);
        const updates = {};
        for (const urlKey in allData) {
          if (allData[urlKey] && typeof allData[urlKey].count !== 'undefined') { // 确保是我们的统计对象
             updates[urlKey] = { count: 0, lastClicked: null };
          }
        }
        await chrome.storage.local.set(updates);
        console.log("已重置所有书签统计");
        loadAndDisplayBookmarks(); // 重新加载数据
      } catch (e) {
        console.error("重置所有书签统计时出错:", e);
      }
    }
  });

  // 初始加载
  loadAndDisplayBookmarks();

  // 监听存储变化，实时更新popup (如果popup是打开状态)
  chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local' && !isEditMode) { // 添加编辑模式检查
      console.log("检测到存储变化，重新加载popup数据");
      loadAndDisplayBookmarks();
    }
  });

  // 添加编辑模式勾选框事件监听器
  if (editModeCheckbox) {
    editModeCheckbox.addEventListener('change', function() {
      isEditMode = this.checked;
      console.log("编辑模式:", isEditMode ? "开启" : "关闭");
      
      // 可选：当退出编辑模式时，刷新一次数据以确保显示最新状态
      if (!isEditMode) {
        loadAndDisplayBookmarks();
      }
    });
  }
});