<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流体渐变背景</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        dark: '#0a0f1c',
                        primary: '#3b82f6',
                        secondary: '#10b981',
                        accent: '#8b5cf6',
                    },
                }
            }
        }
    </script>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background-color: #0a0f1c;
            font-family: 'Inter', sans-serif;
        }
        
        .bg-blob {
            position: fixed;
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, rgba(139, 92, 246, 0.2) 100%);
            border-radius: 50%;
            filter: blur(80px);
            animation: blob-animation 20s infinite ease-in-out;
            z-index: -1;
        }
        
        .bg-blob:nth-child(2) {
            top: 20%;
            right: 10%;
            background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, rgba(59, 130, 246, 0.2) 100%);
            animation-delay: -5s;
            animation-duration: 25s;
        }
        
        .bg-blob:nth-child(3) {
            bottom: 10%;
            left: 20%;
            background: radial-gradient(circle, rgba(139, 92, 246, 0.3) 0%, rgba(16, 185, 129, 0.2) 100%);
            animation-delay: -10s;
            animation-duration: 30s;
        }
        
        @keyframes blob-animation {
            0% {
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                transform: translate(50%, 50%) scale(1.2);
            }
            100% {
                transform: translate(-50%, -50%) scale(1);
            }
        }
        
        .content {
            position: relative;
            z-index: 1;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            text-align: center;
        }
        
        .title {
            font-size: clamp(2rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
            background-image: linear-gradient(90deg, #3b82f6, #8b5cf6);
        }
        
        .subtitle {
            font-size: clamp(1rem, 2vw, 1.5rem);
            color: rgba(255, 255, 255, 0.7);
            max-width: 800px;
            margin-bottom: 2rem;
        }
        
        .btn {
            padding: 0.75rem 2rem;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            color: white;
            border-radius: 9999px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.2);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body>
    <div class="bg-blob" style="top: 50%; left: 50%;"></div>
    <div class="bg-blob"></div>
    <div class="bg-blob"></div>
    
    <div class="content">
        <h1 class="title">流体渐变背景</h1>
        <p class="subtitle">平滑流动的渐变背景，创造出深度和动态感</p>
        <a href="#" class="btn">探索更多</a>
    </div>
</body>
</html>
  