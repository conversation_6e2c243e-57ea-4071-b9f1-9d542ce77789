<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音无水印视频下载工具</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FE2C55',
                        secondary: '#25F4EE',
                        dark: '#121212',
                        light: '#F8F9FA',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'bounce-slow': 'bounce 3s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                    }
                },
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .bg-gradient-douyin {
                background: linear-gradient(135deg, #FE2C55 0%, #25F4EE 100%);
            }
            .hover-scale {
                transition: transform 0.3s ease;
            }
            .hover-scale:hover {
                transform: scale(1.03);
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-md fixed w-full z-50 transition-all duration-300" id="navbar">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fa fa-music text-primary text-2xl"></i>
                <h1 class="text-xl font-bold text-gray-800">抖音无水印下载</h1>
            </div>
            <div class="hidden md:flex items-center space-x-6">
                <a href="#" class="text-gray-600 hover:text-primary transition-colors">首页</a>
                <a href="#" class="text-gray-600 hover:text-primary transition-colors">使用教程</a>
                <a href="#" class="text-gray-600 hover:text-primary transition-colors">常见问题</a>
                <a href="#" class="text-gray-600 hover:text-primary transition-colors">关于我们</a>
            </div>
            <button class="md:hidden text-gray-600 focus:outline-none" id="menu-toggle">
                <i class="fa fa-bars text-xl"></i>
            </button>
        </div>
        <!-- 移动端菜单 -->
        <div class="md:hidden hidden bg-white w-full border-t" id="mobile-menu">
            <div class="container mx-auto px-4 py-2 flex flex-col space-y-3">
                <a href="#" class="text-gray-600 hover:text-primary py-2 transition-colors">首页</a>
                <a href="#" class="text-gray-600 hover:text-primary py-2 transition-colors">使用教程</a>
                <a href="#" class="text-gray-600 hover:text-primary py-2 transition-colors">常见问题</a>
                <a href="#" class="text-gray-600 hover:text-primary py-2 transition-colors">关于我们</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="flex-grow pt-24 pb-12">
        <div class="container mx-auto px-4">
            <!-- 标题和介绍 -->
            <section class="text-center mb-12">
                <h2 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-gray-800 mb-4">
                    抖音无水印<span class="text-primary">视频下载</span>
                </h2>
                <p class="text-gray-600 text-lg max-w-3xl mx-auto">
                    快速、简单地下载抖音无水印视频，保留原始画质，无需注册，完全免费！
                </p>
            </section>

            <!-- 功能区 -->
            <section class="max-w-4xl mx-auto bg-white rounded-xl shadow-lg p-6 md:p-8 mb-12 transform transition-all duration-500 hover:shadow-xl">
                <!-- 输入框和按钮 -->
                <div class="mb-8">
                    <div class="flex flex-col md:flex-row gap-3">
                        <input type="text" id="video-url" placeholder="粘贴抖音分享链接（例如：https://v.douyin.com/abcdef/）" 
                               class="flex-grow px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all">
                        <button id="download-btn" class="bg-primary hover:bg-primary/90 text-white font-medium px-6 py-3 rounded-lg shadow-md hover:shadow-lg transition-all flex items-center justify-center gap-2">
                            <i class="fa fa-download"></i>
                            <span>解析视频</span>
                        </button>
                    </div>
                    <p class="text-gray-500 text-sm mt-2">
                        <i class="fa fa-info-circle"></i> 如何获取抖音视频链接？打开抖音APP → 点击分享按钮 → 复制链接
                    </p>
                </div>

                <!-- 加载状态 -->
                <div id="loading" class="hidden flex-col items-center justify-center py-12">
                    <div class="w-16 h-16 border-4 border-gray-200 border-t-primary rounded-full animate-spin mb-4"></div>
                    <p class="text-gray-600 text-lg">正在解析视频，请稍候...</p>
                </div>

                <!-- 错误提示 -->
                <div id="error-message" class="hidden bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fa fa-exclamation-triangle text-red-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-red-700" id="error-text">
                                请输入有效的抖音视频链接！
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 视频预览区 -->
                <div id="video-preview" class="hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 视频信息 -->
                        <div class="space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="text-xl font-semibold text-gray-800 mb-2" id="video-title">视频标题</h3>
                                <div class="flex items-center text-sm text-gray-600">
                                    <i class="fa fa-picture-o mr-2"></i>
                                    <span id="video-quality">画质：高清</span>
                                </div>
                            </div>
                            
                            <!-- 封面图 -->
                            <div class="relative overflow-hidden rounded-lg hover-scale">
                                <img id="video-cover" src="https://picsum.photos/800/1200" alt="视频封面图" class="w-full h-auto object-cover rounded-lg">
                                <div class="absolute inset-0 bg-black/30 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                                    <button id="play-btn" class="bg-primary/90 text-white w-16 h-16 rounded-full flex items-center justify-center shadow-lg">
                                        <i class="fa fa-play text-xl"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 视频播放和下载 -->
                        <div class="flex flex-col justify-between">
                            <!-- 视频播放器 -->
                            <div class="bg-black rounded-lg overflow-hidden relative" style="padding-bottom: 177.78%;">
                                <video id="video-player" class="absolute inset-0 w-full h-full object-contain" controls></video>
                            </div>

                            <!-- 下载选项 -->
                            <div class="space-y-3 mt-4">
                                <h4 class="text-lg font-medium text-gray-800">下载选项</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <a id="download-video" href="#" target="_blank" class="bg-primary hover:bg-primary/90 text-white font-medium px-4 py-3 rounded-lg shadow-md hover:shadow-lg transition-all flex items-center justify-center gap-2">
                                        <i class="fa fa-download"></i>
                                        <span>无水印视频</span>
                                    </a>
                                    <a id="download-cover" href="#" target="_blank" class="bg-gray-800 hover:bg-gray-700 text-white font-medium px-4 py-3 rounded-lg shadow-md hover:shadow-lg transition-all flex items-center justify-center gap-2">
                                        <i class="fa fa-image"></i>
                                        <span>视频封面</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 使用教程 -->
            <section class="max-w-4xl mx-auto mb-12">
                <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">使用教程</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-xl shadow-md p-6 text-center hover-scale">
                        <div class="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fa fa-link text-primary text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">1. 复制链接</h4>
                        <p class="text-gray-600">打开抖音APP，找到想要下载的视频，点击分享按钮，然后选择"复制链接"</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 text-center hover-scale">
                        <div class="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fa fa-paste text-primary text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">2. 粘贴链接</h4>
                        <p class="text-gray-600">回到本页面，将复制的链接粘贴到上方的输入框中</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 text-center hover-scale">
                        <div class="w-14 h-14 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fa fa-download text-primary text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">3. 下载视频</h4>
                        <p class="text-gray-600">点击"解析视频"按钮，等待解析完成后，点击"无水印视频"按钮下载</p>
                    </div>
                </div>
            </section>

            <!-- 特点 -->
            <section class="max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold text-gray-800 mb-6 text-center">为什么选择我们</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-xl shadow-md p-6 flex items-start hover-scale">
                        <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                            <i class="fa fa-bolt text-primary text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">高速解析</h4>
                            <p class="text-gray-600">采用高性能服务器，快速解析视频链接，无需长时间等待</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 flex items-start hover-scale">
                        <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                            <i class="fa fa-ban text-primary text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">无水印</h4>
                            <p class="text-gray-600">下载的视频不包含抖音水印，保留视频原汁原味</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 flex items-start hover-scale">
                        <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                            <i class="fa fa-lock text-primary text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">安全免费</h4>
                            <p class="text-gray-600">不要求注册或提供个人信息，完全免费使用</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 flex items-start hover-scale">
                        <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mr-4">
                            <i class="fa fa-mobile text-primary text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">移动友好</h4>
                            <p class="text-gray-600">完全响应式设计，在手机、平板和电脑上都能完美使用</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center space-x-2 mb-4">
                        <i class="fa fa-music text-primary text-2xl"></i>
                        <h2 class="text-xl font-bold">抖音无水印下载</h2>
                    </div>
                    <p class="text-gray-400">
                        本工具仅用于个人学习交流，请勿用于商业用途。下载的视频版权归原作者所有。
                    </p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">快速链接</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">首页</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">使用教程</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">常见问题</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-primary transition-colors">关于我们</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">关注我们</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-weibo"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-wechat"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors">
                            <i class="fa fa-qq"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-6 text-center text-gray-500">
                <p>© 2025 抖音无水印下载工具. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <script>
        // DOM元素
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        const navbar = document.getElementById('navbar');
        const videoUrlInput = document.getElementById('video-url');
        const downloadBtn = document.getElementById('download-btn');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('error-message');
        const errorText = document.getElementById('error-text');
        const videoPreview = document.getElementById('video-preview');
        const videoTitle = document.getElementById('video-title');
        const videoQuality = document.getElementById('video-quality');
        const videoCover = document.getElementById('video-cover');
        const videoPlayer = document.getElementById('video-player');
        const videoDownloadLink = document.getElementById('download-video');
        const coverDownloadLink = document.getElementById('download-cover');
        const playBtn = document.getElementById('play-btn');

        // 移动端菜单切换
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // 滚动时导航栏效果
        window.addEventListener('scroll', () => {
            if (window.scrollY > 10) {
                navbar.classList.add('py-2', 'shadow-lg', 'bg-white/95', 'backdrop-blur-sm');
                navbar.classList.remove('py-3', 'shadow-md', 'bg-white');
            } else {
                navbar.classList.add('py-3', 'shadow-md', 'bg-white');
                navbar.classList.remove('py-2', 'shadow-lg', 'bg-white/95', 'backdrop-blur-sm');
            }
        });

        // 视频解析和下载功能
        downloadBtn.addEventListener('click', async () => {
            const url = videoUrlInput.value.trim();
            
            // 验证输入
            if (!url) {
                showError('请输入抖音视频链接！');
                return;
            }
            
            // 提取真实链接
            const realUrl = extractUrl(url);
            if (!realUrl) {
                showError('无效的抖音视频链接，请重新输入！');
                return;
            }
            
            // 显示加载状态
            showLoading(true);
            hideError();
            hideVideoPreview();
            
            try {
                // 调用API解析视频
                const apiUrl = `https://api.xingzhige.com/API/douyin/?url=${encodeURIComponent(realUrl)}`;
                const response = await fetch(apiUrl);
                
                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }
                
                const data = await response.json();
                console.log('API Response:', data); // 添加这行来输出API的完整响应
                
                // 检查API响应
                if (!data || !data.data || !data.data.item || !data.data.item.url) {
                    throw new Error('无法解析视频，请尝试其他链接！');
                }
                
                // 更新UI
                updateVideoPreview(data.data.item);
                showVideoPreview();
            } catch (error) {
                console.error('解析视频时出错:', error);
                showError('解析视频失败，请稍后再试！');
            } finally {
                showLoading(false);
            }
        });

        // 播放按钮点击事件
        playBtn.addEventListener('click', () => {
            videoPlayer.play();
        });

        // 提取URL函数
        function extractUrl(text) {
            // 简单的URL提取正则表达式
            const urlRegex = /https?:\/\/[^\s]+/;
            const match = text.match(urlRegex);
            return match ? match[0] : null;
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            if (show) {
                loading.classList.remove('hidden');
                loading.classList.add('flex');
            } else {
                loading.classList.add('hidden');
                loading.classList.remove('flex');
            }
        }

        // 显示错误信息
        function showError(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            // 添加动画效果
            errorMessage.classList.add('animate-pulse');
            setTimeout(() => {
                errorMessage.classList.remove('animate-pulse');
            }, 1000);
        }

        // 隐藏错误信息
        function hideError() {
            errorMessage.classList.add('hidden');
        }

        // 显示视频预览
        function showVideoPreview() {
            videoPreview.classList.remove('hidden');
            // 添加动画效果
            videoPreview.classList.add('animate-fade-in');
        }

        // 隐藏视频预览
        function hideVideoPreview() {
            videoPreview.classList.add('hidden');
        }

        // 更新视频预览内容
        function updateVideoPreview(item) {
            videoTitle.textContent = item.title || '无标题视频';
            videoQuality.textContent = `画质：${item.quality || '高清'}`;
            videoCover.src = item.cover || 'https://picsum.photos/800/1200';
            videoCover.alt = `视频封面: ${item.title || '无标题视频'}`;
            
            videoPlayer.src = item.url;
            videoPlayer.poster = item.cover || 'https://picsum.photos/800/1200';
            
            videoDownloadLink.href = item.url;
            videoDownloadLink.download = `${item.title || 'douyin_video'}.mp4`;
            
            coverDownloadLink.href = item.cover;
            coverDownloadLink.download = `${item.title || 'video_cover'}.jpg`;
        }
    </script>
</body>
</html>
