<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Gradient Background</title>
<script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="m-0 p-0 overflow-hidden">
    <canvas id="gradientCanvas" class="w-full h-screen block"></canvas>
    
    <div class="fixed top-4 left-4 z-10">
        <button id="toggleState" class="px-6 py-3 bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30 rounded-full text-white font-medium hover:bg-opacity-30 transition-all duration-300">
            State: <span id="stateText">Idle</span>
        </button>
    </div>

    <script>
        const canvas = document.getElementById('gradientCanvas');
        const gl = canvas.getContext('webgl');
        
        let uSpeakingState = 0.0;
        let targetState = 0.0;
        
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            gl.viewport(0, 0, canvas.width, canvas.height);
        }
        
        const vertexShaderSource = `
            attribute vec2 position;
            void main() {
                gl_Position = vec4(position, 0.0, 1.0);
            }
        `;
        
        const fragmentShaderSource = `
            precision mediump float;
            uniform vec2 iResolution;
            uniform float iTime;
            uniform float uSpeakingState;
            
            #define S(a,b,t) smoothstep(a,b,t)
            
            vec2 hash(vec2 p) {
                p = vec2(dot(p,vec2(2127.1,81.17)), dot(p,vec2(1269.5,283.37)));
                return fract(sin(p)*43758.5453);
            }

            mat2 Rot(float a) {
                float s = sin(a);
                float c = cos(a);
                return mat2(c, -s, s, c);
            }

            float noise(in vec2 p) {
                vec2 i = floor(p);
                vec2 f = fract(p);
                
                vec2 u = f*f*(3.0-2.0*f);
                float n = mix(mix(dot(-1.0+2.0*hash(i + vec2(0.0,0.0)), f - vec2(0.0,0.0)), 
                              dot(-1.0+2.0*hash(i + vec2(1.0,0.0)), f - vec2(1.0,0.0)), u.x),
                          mix(dot(-1.0+2.0*hash(i + vec2(0.0,1.0)), f - vec2(0.0,1.0)), 
                              dot(-1.0+2.0*hash(i + vec2(1.0,1.0)), f - vec2(1.0,1.0)), u.x), u.y);
                return 0.5 + 0.5*n;
            }
            
            void main() {
                vec2 uv = gl_FragCoord.xy/iResolution.xy;
                float ratio = iResolution.x / iResolution.y;
                vec2 tuv = uv - 0.5;
                
                float degree = noise(vec2(iTime*.1, tuv.x*tuv.y));
                tuv.y *= 1./ratio;
                tuv *= Rot(radians((degree-.25)*720.+180.));
                tuv.y *= ratio;
                
                float idleFreq = 8.0;
                float speakingFreq = 12.0;
                float idleAmp = 100.0;
                float speakingAmp = 40.0;
                float idleSpeed = 1.0;
                float speakingSpeed = 2.5;
                float idleNoise = 0.1;
                float speakingNoise = 0.15;
                
                float frequency = mix(idleFreq, speakingFreq, uSpeakingState);
                float amplitude = mix(idleAmp, speakingAmp, uSpeakingState);
                float speed = mix(idleSpeed, speakingSpeed, uSpeakingState);
                float noiseAmount = mix(idleNoise, speakingNoise, uSpeakingState);
                
                float t = iTime * speed;
                tuv.x += sin(tuv.y*frequency+t)/amplitude;
                tuv.y += sin(tuv.x*frequency*1.5+t)/(amplitude*.5);
                
                float noiseScale = 10.0;
                tuv += (noise(tuv * noiseScale + t) - 0.5) * noiseAmount;
                
                vec3 colorPurple = vec3(0.7, 0.3, 0.9);
                vec3 colorOrange = vec3(1.0, 0.6, 0.2);
                vec3 colorBlue = vec3(0.2, 0.4, 0.9);
                
                vec3 layer1 = mix(colorPurple, colorOrange, S(-0.2, 0.3, tuv.x));
                layer1 = mix(layer1, colorBlue, S(0.2, 0.6, tuv.y));
                
                gl_FragColor = vec4(layer1, 1.0);
            }
        `;
        
        function createShader(gl, type, source) {
            const shader = gl.createShader(type);
            gl.shaderSource(shader, source);
            gl.compileShader(shader);
            return shader;
        }
        
        const vertexShader = createShader(gl, gl.VERTEX_SHADER, vertexShaderSource);
        const fragmentShader = createShader(gl, gl.FRAGMENT_SHADER, fragmentShaderSource);
        
        const program = gl.createProgram();
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);
        
        const positionBuffer = gl.createBuffer();
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([
            -1, -1, 1, -1, -1, 1, 1, 1
        ]), gl.STATIC_DRAW);
        
        const positionLocation = gl.getAttribLocation(program, 'position');
        const iResolutionLocation = gl.getUniformLocation(program, 'iResolution');
        const iTimeLocation = gl.getUniformLocation(program, 'iTime');
        const uSpeakingStateLocation = gl.getUniformLocation(program, 'uSpeakingState');
        
        function render(time) {
            uSpeakingState += (targetState - uSpeakingState) * 0.02;
            
            gl.clear(gl.COLOR_BUFFER_BIT);
            gl.useProgram(program);
            
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);
            
            gl.uniform2f(iResolutionLocation, canvas.width, canvas.height);
            gl.uniform1f(iTimeLocation, time * 0.001);
            gl.uniform1f(uSpeakingStateLocation, uSpeakingState);
            
            gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
            requestAnimationFrame(render);
        }
        
        const toggleButton = document.getElementById('toggleState');
        const stateText = document.getElementById('stateText');
        
        toggleButton.addEventListener('click', () => {
            targetState = targetState === 0.0 ? 1.0 : 0.0;
            stateText.textContent = targetState === 1.0 ? 'Speaking' : 'Idle';
        });
        
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();
        requestAnimationFrame(render);
    </script>
</body>
</html>